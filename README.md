# Guitar Club Web App

Интерактивное веб-приложение для обучения игре на гитаре, включающее набор тренажеров для развития музыкальных навыков.

## О проекте

Guitar Club Web App - это экосистема интерактивных тренажеров для гитаристов, разработанная с использованием современных веб-технологий. Приложение помогает музыкантам развивать навыки распознавания нот, аккордов, интервалов и улучшать музыкальный слух.

## Основные функции

- **Тренажер "Назови аккорд"** - развивает навык распознавания аккордов по их диаграмме
- **Тренажер аккордов** - помогает выучить различные аккорды и их аппликатуры
- **Тренажер "Найди ноту/интервал"** - улучшает знание расположения нот на грифе гитары
- **Настройка распознавания нот** - тренирует навык определения нот на слух
- **Тренажер высоты звука** - развивает музыкальный слух
- **Редактор грифа** - позволяет создавать и редактировать диаграммы аккордов
- **Генератор случайных аккордов** - помогает в изучении разнообразных аккордов

## Установка и запуск

```bash
# Переход в директорию проекта
cd guitar-app

# Установка зависимостей
npm install

# Запуск в режиме разработки
npm start
```

## Документация

Проект имеет краткую документацию для разработки:

- [Руководство по разработке](documents/DEV_GUIDE.md) - архитектура проекта и принципы разработки
- [Руководство по UI](documents/UI_GUIDE.md) - дизайн и UI компоненты
- [Технический стек](documents/TECH_STACK.md) - используемые технологии и библиотеки
- [Дорожная карта](documents/ROADMAP.md) - планы по развитию проекта
- [Алгоритмы](documents/ALGORITHMS.md) - специализированные алгоритмы проекта
- [Руководство по работе с ИИ](documents/AI_GUIDE.md) - инструкции для ИИ по работе с проектом
- [Шаблоны запросов для ИИ](documents/AI_PROMPTS.md) - как эффективно формулировать запросы к ИИ

## Статус проекта

Проект находится в активной разработке. В настоящее время ведется миграция компонентов на библиотеку shadcn/ui для улучшения пользовательского интерфейса и обеспечения единого стиля.
