# Алгоритмы и методы, используемые в проекте

Этот документ описывает основные алгоритмы и методы, используемые в проекте Guitar Club Web App для решения специфических задач музыкальной обработки и анализа.

## 1. Настройка распознавания нот (Pitch Detection)

Настройка распознавания нот (определение высоты основного тона, $f_0$) — ключевая задача в обработке аудиосигналов для музыкальных приложений, таких как гитарные тюнеры, обучающие программы и системы транскрипции.

### 1.1. Методы во временной области (Time Domain Methods)

Эти методы анализируют форму волны сигнала непосредственно во времени.

* **Автокорреляционная функция (Autocorrelation Function - ACF)**
    * **Принцип:** Измеряет периодичность сигнала путем сравнения его с его же сдвинутой копией. Пик автокорреляции (кроме нулевого) соответствует периоду основного тона $T$. Частота вычисляется как $f_0 = 1/T$.
    * **Преимущества:** Относительно прост в понимании и реализации. Хорошо работает с чистыми тонами.
    * **Недостатки:** Вычислительно затратен, чувствителен к шуму, возможны ошибки на октаву (путает $f_0$ с $2f_0$ или $f_0/2$). Сложности при перекрытии частот.

* **McLeod Pitch Method (MPM)**
    * **Принцип:** Использует нормализованную функцию разницы квадратов (часть алгоритма YIN) для нахождения пиков, соответствующих периоду.
    * **Преимущества:** Высокая точность на всем диапазоне гитары, отличная работа с атакой звука струны, устойчивость к октавным ошибкам.
    * **Применение в проекте:** Используется в компоненте распознавания нот через библиотеку pitchy.

### 1.2. Методы в частотной области (Frequency Domain Methods)

Эти методы анализируют частотный состав сигнала, полученный обычно с помощью преобразования Фурье.

* **Быстрое преобразование Фурье (Fast Fourier Transform - FFT)**
    * **Принцип:** Преобразует фрагмент сигнала во временной области в спектр частот. Основная частота $f_0$ обычно соответствует самому низкому значимому пику.
    * **Преимущества:** Дает полную картину частотного состава. Стандартный инструмент. Эффективен для анализа комплексных сигналов.
    * **Недостатки:** Компромисс между временным и частотным разрешением (зависит от размера окна). Сложности с выделением $f_0$ при сильных гармониках.

* **Гармоническое произведение спектра (Harmonic Product Spectrum - HPS)**
    * **Принцип:** Улучшение FFT. Умножает спектр на его сжатые копии (в 2, 3, ... раза), усиливая пик $f_0$ за счет совпадения с его гармониками.
    * **Преимущества:** Уменьшает ошибки на октаву, надежнее выделяет $f_0$. Хорошо справляется с гармоническими сигналами.
    * **Применение в проекте:** Использовался в ранних версиях компонента распознавания нот.

### 1.3. Рекомендации для распознавания нот гитары в реальном времени

#### Выбранный алгоритм: McLeod Pitch Method (MPM)

**Настройки алгоритма**
- **Размер буфера**: 4096 отсчетов при 44.1 кГц (≈93 мс)
- **Перекрытие окон**: 50% (2048 отсчетов)
- **Порог достоверности**: 0.85-0.9 (настраивается в зависимости от уровня шума)

**Предобработка сигнала**
- **Bandpass фильтр**: 80-1200 Гц (охватывает диапазон основных частот гитары)
- **Нормализация громкости**: адаптивная с быстрой атакой и медленным релизом
- **Медианный фильтр**: для подавления импульсных шумов (опционально)

**Постобработка результатов**
- **Map-to-note**: преобразование частоты в ближайшую ноту с учетом погрешности
- **Дебаунсинг**: минимальное время удержания ноты 50-70 мс
- **Сглаживание**: медианное сглаживание с окном 3-5 измерений
- **Проверка переходов**: ограничение максимального скачка частот за один фрейм (для исключения октавных ошибок)

## 2. Генерация и анализ аккордов

### 2.1. Генерация аккордов по формулам

В проекте используется алгоритм генерации аккордов на основе формул интервалов относительно корневой ноты.

**Принцип работы:**
1. Определение корневой ноты аккорда (например, C, D, E и т.д.)
2. Применение формулы интервалов для конкретного типа аккорда:
   - Мажорный аккорд: 1-3-5 (корень, большая терция, чистая квинта)
   - Минорный аккорд: 1-b3-5 (корень, малая терция, чистая квинта)
   - Доминантсептаккорд: 1-3-5-b7 (корень, большая терция, чистая квинта, малая септима)
3. Преобразование интервалов в конкретные ноты
4. Размещение нот на грифе гитары с учетом аппликатуры

**Применение в проекте:**
- Используется в компоненте генератора случайных аккордов
- Применяется в тренажере аккордов для создания различных вариаций аккордов

### 2.2. Алгоритм поиска оптимальной аппликатуры

Для размещения аккорда на грифе гитары используется алгоритм поиска оптимальной аппликатуры.

**Критерии оптимальности:**
- Минимальное растяжение пальцев (компактность аккорда)
- Включение всех необходимых нот аккорда
- Возможность исполнения аккорда (физическая играбельность)
- Предпочтение открытых струн, где это возможно

**Принцип работы:**
1. Генерация всех возможных комбинаций размещения нот аккорда на грифе
2. Фильтрация комбинаций по критерию играбельности
3. Оценка каждой комбинации по компактности и звучанию
4. Выбор оптимальной комбинации

**Применение в проекте:**
- Используется в компоненте ChordDiagram для отображения аппликатуры аккорда
- Применяется в редакторе грифа для предложения вариантов аппликатуры

## 3. Алгоритмы работы с музыкальной теорией

### 3.1. Преобразование между нотами и частотами

Для преобразования между нотами и их частотами используется формула равномерно темперированного строя:

$$f = f_0 \times 2^{n/12}$$

где:
- $f$ - частота искомой ноты
- $f_0$ - опорная частота (обычно A4 = 440 Гц)
- $n$ - количество полутонов от опорной ноты (положительное или отрицательное)

**Применение в проекте:**
- Используется в компоненте распознавания нот для преобразования частоты в ноту
- Применяется в тренажере высоты звука для генерации звуков нот

### 3.2. Алгоритм определения интервалов

Для определения музыкального интервала между двумя нотами используется следующий алгоритм:

1. Преобразование нот в их числовые представления (MIDI-номера)
2. Вычисление разницы между числовыми представлениями
3. Определение типа интервала на основе разницы:
   - 0 полутонов: прима
   - 1 полутон: малая секунда
   - 2 полутона: большая секунда
   - 3 полутона: малая терция
   - 4 полутона: большая терция
   - и т.д.

**Применение в проекте:**
- Используется в компоненте GuitarNoteIntervalGame для определения интервалов
- Применяется в тренажере высоты звука для генерации интервалов

## 4. Алгоритмы визуализации

### 4.1. Визуализация грифа гитары

Для визуализации грифа гитары используется алгоритм генерации SVG на основе данных о нотах и ладах.

**Принцип работы:**
1. Определение размеров и пропорций грифа
2. Расчет координат для струн, ладов и маркеров
3. Генерация SVG-элементов для каждой части грифа
4. Добавление интерактивности (выделение нот, ладов, струн)

**Применение в проекте:**
- Используется в компоненте GuitarFretboard для отображения грифа
- Применяется в компоненте GuitarNoteIntervalGame для визуализации заданий

### 4.2. Визуализация аккордовых диаграмм

Для визуализации аккордовых диаграмм используется алгоритм генерации SVG на основе данных об аппликатуре аккорда.

**Принцип работы:**
1. Определение размеров и пропорций диаграммы
2. Расчет координат для струн, ладов и маркеров
3. Генерация SVG-элементов для каждой части диаграммы
4. Добавление маркеров для пальцев и открытых/закрытых струн

**Применение в проекте:**
- Используется в компоненте ChordDiagram для отображения аккордов
- Применяется в компоненте NameTheChord для визуализации заданий

## 5. Алгоритмы генерации тренировочных заданий

### 5.1. Алгоритм генерации случайных аккордов

Для генерации случайных аккордов используется алгоритм, учитывающий различные параметры и ограничения.

**Принцип работы:**
1. Выбор случайной корневой ноты из заданного набора
2. Выбор случайного типа аккорда из заданного набора
3. Генерация аккорда по формуле
4. Проверка на соответствие ограничениям (сложность, играбельность)
5. Если аккорд не соответствует ограничениям, повторение с шага 1

**Применение в проекте:**
- Используется в компоненте RandomChordGenerator
- Применяется в тренажере аккордов для генерации заданий

### 5.2. Алгоритм генерации последовательностей нот

Для генерации последовательностей нот используется алгоритм, учитывающий музыкальную теорию и педагогические принципы.

**Принцип работы:**
1. Выбор тональности или набора нот
2. Генерация последовательности с учетом музыкальных закономерностей
3. Постепенное увеличение сложности в зависимости от прогресса пользователя
4. Повторение проблемных нот с большей вероятностью

**Применение в проекте:**
- Используется в компоненте GuitarNoteIntervalGame
- Применяется в тренажере высоты звука

## 6. Возможные улучшения алгоритмов

### 6.1. Улучшения алгоритма распознавания нот

- **Динамическое изменение размера окна** для разных частот (нот)
- **Самообучающийся порог достоверности** для адаптации к особенностям игры пользователя
- **Разделение сигнала на субполосы** для оптимизации анализа разных диапазонов частот
- **Адаптивное шумоподавление** для изоляции гитарного сигнала от шума
- **Выделение транзиентов** для точного определения момента начала новой ноты
- **Анализ гармонического профиля** для учета уникальных тембральных характеристик конкретной гитары

### 6.2. Улучшения алгоритмов генерации заданий

- **Адаптивная сложность** на основе истории ответов пользователя
- **Персонализированные последовательности** с фокусом на проблемных областях
- **Музыкально осмысленные последовательности** вместо полностью случайных
- **Интеграция с реальными музыкальными произведениями** для более практичного обучения
