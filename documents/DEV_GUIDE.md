# Руководство по разработке

Краткое руководство по архитектуре проекта и принципам разработки.

## Структура проекта

```
guitar-app/
├── public/                  # Статические файлы
├── src/
│   ├── components/          # UI компоненты
│   │   ├── ChordSettingsNew.tsx # Новая версия настроек аккордов
│   │   └── ui/              # Компоненты shadcn/ui
│   │
│   ├── constants/           # Константы и данные
│   │   ├── chordFormulas.ts # Формулы аккордов
│   │   ├── chords.ts        # Данные аккордов
│   │   ├── guitarNotes.ts   # Ноты на гитаре
│   │   └── scales.ts        # Гаммы
│   │
│   ├── contexts/            # Контексты React
│   │   ├── AppContext.tsx   # Глобальный контекст приложения
│   │   └── ThemeContext.tsx # Контекст темы
│   │
│   ├── features/            # Функциональные модули
│   │   ├── ChordTrainer.tsx      # Тренажер аккордов (старая версия)
│   │   ├── ChordTrainerNew.tsx   # Тренажер аккордов (новая версия с shadcn/ui)
│   │   ├── GuitarNeckEditor.tsx  # Редактор грифа
│   │   ├── GuitarNoteIntervalGame.tsx # Игра на ноты и интервалы
│   │   ├── HomePage.tsx          # Главная страница
│   │   ├── NameTheChord.tsx      # Назови аккорд (старая версия)
│   │   ├── NameTheChordNew.tsx   # Назови аккорд (новая версия с shadcn/ui)
│   │   ├── NoteRecognition.tsx   # Настройка распознавания нот
│   │   ├── PitchTrainer.tsx      # Тренажер высоты звука
│   │   ├── RandomChordGenerator.tsx # Генератор случайных аккордов
│   │   └── ScoreCounter.tsx      # Счетчик очков
│   │
│   ├── shared/              # Общие компоненты и утилиты
│   │   ├── components/      # Общие компоненты
│   │   ├── hooks/           # Общие хуки
│   │   ├── types/           # Типы TypeScript
│   │   ├── ui/              # Общие UI компоненты
│   │   └── utils/           # Утилиты
│   │
│   ├── App.tsx              # Корневой компонент
│   ├── index.css            # Глобальные стили
│   └── main.tsx             # Точка входа
│
├── index.html               # HTML шаблон
├── package.json             # Зависимости и скрипты
├── tsconfig.json            # Конфигурация TypeScript
└── vite.config.ts           # Конфигурация Vite
```

## Архитектурные принципы

1. **Организация по функциональным модулям (features)**
   - Каждый тренажер - отдельный модуль в директории `features`
   - Общие компоненты в директории `shared/components`

2. **Разделение на презентационные компоненты и контейнеры**
   - Презентационные компоненты отвечают только за отображение
   - Контейнеры управляют состоянием и логикой

3. **Управление состоянием**
   - Глобальное состояние: React Context API с useReducer
   - Локальное состояние: useState или useReducer
   - Глобальное состояние только для общих данных (тема, настройки)
   - Изолированное состояние для каждого тренажера

## Соглашения по именованию

- **Файлы и директории**
  - Директории: kebab-case (`chord-trainer`)
  - Компоненты React: PascalCase (`ChordTrainer.tsx`)
  - Утилиты и хуки: camelCase (`useChords.ts`)
  - Типы: PascalCase с суффиксом `.types.ts` (`chord.types.ts`)

- **Компоненты**
  - Имена компонентов: PascalCase (`ChordTrainer`)
  - Пропсы: интерфейс с суффиксом `Props` (`ChordTrainerProps`)
  - Состояния: интерфейс с суффиксом `State` (`ChordState`)

- **Хуки**
  - Имена хуков: начинаются с `use` (`useChords`)
  - Возвращаемые значения: объект с именованными свойствами

## Лучшие практики

1. **Оптимизация производительности**
   - Используйте useMemo для кэширования результатов сложных вычислений
   - Используйте useCallback для стабилизации функций
   - Применяйте React.memo для предотвращения лишних рендеров

2. **Типизация**
   - Используйте строгую типизацию TypeScript
   - Избегайте типов `any` и `unknown`
   - Создавайте интерфейсы для всех пропсов компонентов

3. **Чистый код**
   - Один компонент - одна задача
   - Избегайте дублирования кода (DRY)
   - Стремитесь к простоте (KISS)

4. **Работа с состоянием**
   ```tsx
   // Локальное состояние
   const [currentChord, setCurrentChord] = useState<Chord | null>(null);
   
   // Глобальное состояние
   const { state, dispatch } = useAppContext();
   const { isAudioEnabled } = state.settings;
   dispatch({ type: 'TOGGLE_AUDIO' });
   ```

## Миграция на shadcn/ui

- Новые версии компонентов создаются с суффиксом "New" (`NameTheChordNew.tsx`)
- Старые версии сохраняются для обратной совместимости
- Компоненты shadcn/ui находятся в директории `components/ui`

## Статус миграции компонентов

| Компонент | Статус | Новая версия |
|-----------|--------|--------------|
| Name The Chord | ✅ Мигрирован | NameTheChordNew.tsx |
| Chord Trainer | ✅ Мигрирован | ChordTrainerNew.tsx |
| Guitar Note/Interval Game | ⏳ В процессе | - |
| Pitch Trainer | ⏳ В процессе | - |
| Note Recognition | ⏳ В процессе | - |
| Guitar Neck Editor | ⏳ В процессе | - |
| Score Counter | ⏳ В процессе | - |
| Random Chord Generator | ⏳ В процессе | - |
