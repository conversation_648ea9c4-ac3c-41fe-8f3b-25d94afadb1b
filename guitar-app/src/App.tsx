import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Контексты
import { AppProvider } from './contexts/AppContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { AudioSettingsProvider } from './shared/contexts/AudioSettingsContext';

// Компоненты из features
import GuitarNoteIntervalGame from './features/GuitarNoteIntervalGame';
import PitchTrainer from './features/PitchTrainer';
import GuitarPitchTrainer from './features/GuitarPitchTrainer';
import NoteRecognition from './features/NoteRecognition';
import ChordTrainer from './features/ChordTrainer';
import ChordTrainerNew from './features/ChordTrainerNew';
import NameTheChord from './features/NameTheChord';
import NameTheChordNew from './features/NameTheChordNew';
import GuitarNeckEditor from './features/GuitarNeckEditor';
import MusicTheory from './features/MusicTheory';
import ScoreCounter from './features/ScoreCounter';
import RandomChordGenerator from './features/RandomChordGenerator';
import HomePage from './features/HomePage';

// UI компоненты
import HamburgerMenu from './shared/ui/hamburger-menu';

// Примеры компонентов (для разработки)
import TypographyExamples from './examples/TypographyExamples';
import UIComponentsExamples from './examples/UIComponentsExamples';

function App() {
  return (
    <ThemeProvider>
      <AppProvider>
        <AudioSettingsProvider>
          <Router>
            <div className="App min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
              <div className="absolute top-4 left-4 z-50">
                <HamburgerMenu />
              </div>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/guitar-fretboard" element={<GuitarNoteIntervalGame />} />
                <Route path="/pitch-trainer" element={<PitchTrainer />} />
                <Route path="/guitar-pitch-trainer" element={<GuitarPitchTrainer />} />
                <Route path="/note-recognition" element={<NoteRecognition />} />
                <Route path="/chord-trainer" element={<ChordTrainerNew />} />
                <Route path="/chord-trainer-old" element={<ChordTrainer />} />
                <Route path="/name-the-chord" element={<NameTheChordNew />} />
                <Route path="/name-the-chord-old" element={<NameTheChord />} />
                <Route path="/guitar-neck-editor" element={<GuitarNeckEditor />} />
                <Route path="/score-counter" element={<ScoreCounter />} />
                <Route path="/music-theory" element={<MusicTheory />} />
                <Route path="/midi-chord-generator" element={<RandomChordGenerator />} />
                <Route path="/typography" element={<TypographyExamples />} />
                <Route path="/ui-components" element={<UIComponentsExamples />} />
                {/* Редиректы для старых маршрутов */}
                <Route path="/guitar-note-game" element={<Navigate to="/guitar-fretboard" replace />} />
                <Route path="/guitar-interval-game" element={<Navigate to="/guitar-fretboard" replace />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </div>
          </Router>
        </AudioSettingsProvider>
      </AppProvider>
    </ThemeProvider>
  );
}

export default App;
