import { useCallback, useMemo, useEffect, useReducer, useRef } from 'react';
// Импортируем константы из нового модуля musicTheory
import {
  GUITAR_NOTES,
  GUITAR_TUNING,
  NOTE_NAMES,
  formatNote as formatNoteFromTheory,
  calculateTargetNote as calculateTargetNoteFromTheory
} from '../constants/musicTheory';
import BackButton from '../shared/ui/back-button';
// Импортируем общий компонент грифа
import GuitarFretboard, { NoteDisplay } from '../shared/components/GuitarFretboard';
// Импортируем компонент переключателя
import { Switch } from '../shared/ui/switch';
// Импортируем AudioAnalyzer для распознавания звука
import { AudioAnalyzer } from '../shared/utils/AudioAnalyzer';
// Импортируем контекст аудио настроек
import { useAudioSettings } from '../shared/contexts/AudioSettingsContext';

// Используем функцию formatNote из musicTheory.ts
const formatNote = formatNoteFromTheory;

// Сгруппированные интервалы по степеням
const GROUPED_INTERVALS = {
  '2': ['b2', '2', '#2'],
  '3': ['b3', '3'],
  '4': ['4', '#4'],
  '5': ['b5', '5', '#5'],
  '6': ['b6', '6'],
  '7': ['b7', '7'],
};

// Интервалы в полутонах
const INTERVALS_SEMITONES: Record<string, number> = {
  '1': 0,
  'b2': 1, '2': 2, '#2': 3,
  'b3': 3, '3': 4,
  '4': 5, '#4': 6,
  'b5': 6, '5': 7, '#5': 8,
  'b6': 8, '6': 9,
  'b7': 10, '7': 11,
};

// Типы для локального состояния игры
interface GameState {
  isPlaying: boolean;
  startTime: number | null;
  isMicrophoneEnabled: boolean;
  statistics: {
    score: number;
    total: number;
    accuracy: number;
    speed: number;
    time: number;
  };
  isIntervalMode: boolean;
  noteGame: {
    targetNote: string;
    targetString: number;
    targetFret: number;
    clickedFret: { string: number, fret: number } | null;
    clickedNote: string;
    isCorrect: boolean | null;
    animate: boolean;
  };
  intervalGame: {
    startNote: string;
    targetInterval: string;
    targetNote: string;
    targetString: number;
    clickedFret: { string: number; fret: number } | null;
    rootFret: { string: number; fret: number } | null;
    isCorrect: boolean | null;
    animate: boolean;
    useSharp: boolean;
    foundRoot: boolean;
  };
}

// Типы для действий
type GameAction =
  | { type: 'START_GAME' }
  | { type: 'STOP_GAME' }
  | { type: 'UPDATE_STATS'; payload: { correct?: boolean, time?: number, speed?: number } }
  | { type: 'SET_TARGET_NOTE'; payload: { note: string, string: number, fret: number } }
  | { type: 'SET_CLICKED_FRET'; payload: { string: number, fret: number, note: string, isCorrect: boolean } | null }
  | { type: 'SET_ANIMATE'; payload: boolean }
  | { type: 'TOGGLE_INTERVAL_MODE'; payload: boolean }
  | { type: 'TOGGLE_MICROPHONE'; payload: boolean }
  | { type: 'SET_INTERVAL_STATE'; payload: Partial<GameState['intervalGame']> };

// Начальное состояние
const initialState: GameState = {
  isPlaying: false,
  startTime: null,
  isMicrophoneEnabled: false,
  isIntervalMode: false,
  statistics: {
    score: 0,
    total: 0,
    accuracy: 0,
    speed: 0,
    time: 0,
  },
  noteGame: {
    targetNote: '',
    targetString: 0,
    targetFret: 0,
    clickedFret: null,
    clickedNote: '',
    isCorrect: null,
    animate: false,
  },
  intervalGame: {
    startNote: '',
    targetInterval: '',
    targetNote: '',
    targetString: 0,
    clickedFret: null,
    rootFret: null,
    isCorrect: null,
    animate: false,
    useSharp: true,
    foundRoot: false,
  },
};

// Оптимизированный редьюсер
function gameReducer(state: GameState, action: GameAction): GameState {
  switch (action.type) {
    case 'START_GAME':
      return {
        ...state,
        isPlaying: true,
        startTime: Date.now(),
        statistics: { score: 0, total: 0, accuracy: 0, speed: 0, time: 0 },
        noteGame: {
          ...state.noteGame,
          targetNote: '',
          targetString: 0,
          targetFret: 0,
          clickedFret: null,
          clickedNote: '',
          isCorrect: null,
          animate: false,
        },
        intervalGame: {
          ...state.intervalGame,
          startNote: '',
          targetInterval: '',
          targetNote: '',
          targetString: 0,
          clickedFret: null,
          rootFret: null,
          isCorrect: null,
          animate: false,
          foundRoot: false,
        },
      };

    case 'STOP_GAME':
      return { ...state, isPlaying: false };

    case 'UPDATE_STATS': {
      const { correct, time, speed } = action.payload;
      let newState = { ...state };

      // Обновляем счет и общее количество, если указан параметр correct
      if (correct !== undefined) {
        const newScore = correct ? state.statistics.score + 1 : state.statistics.score;
        const newTotal = state.statistics.total + 1;
        const newAccuracy = newScore > 0 ? Math.round((newScore / newTotal) * 100) : 0;

        newState = {
          ...newState,
          statistics: {
            ...newState.statistics,
            score: newScore,
            total: newTotal,
            accuracy: newAccuracy,
          }
        };
      }

      // Обновляем время, если указан параметр time
      if (time !== undefined) {
        newState = {
          ...newState,
          statistics: {
            ...newState.statistics,
            time: newState.statistics.time + time,
          }
        };
      }

      // Обновляем скорость, если указан параметр speed
      if (speed !== undefined) {
        newState = {
          ...newState,
          statistics: {
            ...newState.statistics,
            speed,
          }
        };
      }

      return newState;
    }

    case 'SET_TARGET_NOTE':
      return {
        ...state,
        noteGame: {
          ...state.noteGame,
          targetNote: action.payload.note,
          targetString: action.payload.string,
          targetFret: action.payload.fret,
          animate: true,
        },
      };

    case 'SET_CLICKED_FRET':
      return {
        ...state,
        noteGame: {
          ...state.noteGame,
          clickedFret: action.payload ? {
            string: action.payload.string,
            fret: action.payload.fret,
          } : null,
          clickedNote: action.payload ? action.payload.note : '',
          isCorrect: action.payload ? action.payload.isCorrect : null,
        },
      };

    case 'SET_ANIMATE':
      return {
        ...state,
        noteGame: { ...state.noteGame, animate: action.payload },
      };

    case 'TOGGLE_INTERVAL_MODE':
      return {
        ...state,
        isIntervalMode: action.payload,
      };

    case 'TOGGLE_MICROPHONE':
      return {
        ...state,
        isMicrophoneEnabled: action.payload,
      };

    case 'SET_INTERVAL_STATE':
      return {
        ...state,
        intervalGame: {
          ...state.intervalGame,
          ...action.payload,
        },
      };

    default:
      return state;
  }
}

/**
 * GuitarNoteIntervalGame Component
 *
 * Объединенный компонент для игр "Найди ноту" и "Найди интервал"
 * с возможностью переключения между режимами
 */
const GuitarNoteIntervalGame = () => {
  // Используем локальный редьюсер вместо глобального контекста
  const [state, dispatch] = useReducer(gameReducer, initialState);

  // Получаем данные из локального состояния
  const { isPlaying, isIntervalMode, isMicrophoneEnabled } = state;
  const { score, total, accuracy, speed, time } = state.statistics;

  // Аудио анализатор и настройки
  const audioAnalyzerRef = useRef<AudioAnalyzer | null>(null);
  const { settings } = useAudioSettings();

  // Реф для предотвращения множественных срабатываний
  const isProcessingAnswerRef = useRef<boolean>(false);

  // Реф для стабильной ссылки на handleNoteDetected
  const handleNoteDetectedRef = useRef<(note: string, frequency: number) => void>(() => {});

  // Данные для режима нот
  const {
    targetNote,
    targetString,
    targetFret,
    clickedFret,
    isCorrect,
    animate
  } = state.noteGame;

  // Данные для режима интервалов
  const intervalState = state.intervalGame;

  // --- Callbacks для режима "Найди ноту" ---

  /**
   * Нормализует ноту (бемоли в диезы) для корректного сравнения
   */
  const normalizeNote = useCallback((note: string): string => {
    if (!note) return note;

    // Проверяем, есть ли октава в конце (цифра)
    const hasOctave = /\d$/.test(note);

    if (hasOctave) {
      // Нота с октавой (например, "Ab2")
      const noteName = note.slice(0, -1); // Имя ноты без октавы
      const octave = note.slice(-1); // Октава

      const flatToSharpMap: { [key: string]: string } = {
        'Ab': 'G#',
        'Bb': 'A#',
        'Db': 'C#',
        'Eb': 'D#',
        'Gb': 'F#',
      };

      if (flatToSharpMap[noteName]) {
        return flatToSharpMap[noteName] + octave;
      }
    } else {
      // Нота без октавы (например, "Ab")
      const flatToSharpMap: { [key: string]: string } = {
        'Ab': 'G#',
        'Bb': 'A#',
        'Db': 'C#',
        'Eb': 'D#',
        'Gb': 'F#',
      };

      if (flatToSharpMap[note]) {
        return flatToSharpMap[note];
      }
    }

    // Дополнительно заменяем символ ♯ на #
    return note.replace('♯', '#');
  }, []);



  /**
   * Генерирует случайное число в заданном диапазоне
   */
  const getRandomInt = useCallback((min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }, []);

  /**
   * Вычисляет ноту на заданной струне и ладу
   */
  const getNoteAtPosition = useCallback((stringIndex: number, fretIndex: number, useSharp: boolean = true) => {
    // Используем функцию из musicTheory.ts, но адаптируем индексы струн
    // В компоненте струны нумеруются от 1 до 6 (сверху вниз), а в musicTheory.ts - от 1 до 6 (снизу вверх)

    // Получаем базовую ноту без форматирования
    const baseNote = NOTE_NAMES[
      (NOTE_NAMES.indexOf(GUITAR_TUNING[6 - stringIndex]) + fretIndex) % NOTE_NAMES.length
    ];

    // Форматируем ноту с учетом флага useSharp
    return formatNote(baseNote, useSharp);
  }, []);

  /**
   * Добавляет октаву к ноте на основе позиции на грифе
   */
  const addOctaveToNote = useCallback((noteName: string, string: number, fret: number) => {
    // Базовые октавы для открытых струн (стандартный строй)
    const openStringOctaves = [4, 3, 3, 3, 2, 2]; // E4, B3, G3, D3, A2, E2

    // Получаем базовую октаву для струны
    const baseOctave = openStringOctaves[string - 1];

    // Вычисляем изменение октавы на основе лада
    // Каждые 12 ладов = +1 октава
    const octaveChange = Math.floor(fret / 12);

    // Проверяем, нужно ли увеличить октаву из-за перехода через C
    const openStringNote = GUITAR_TUNING[6 - string]; // Инвертируем индекс
    const openStringIndex = NOTE_NAMES.indexOf(openStringNote);
    const currentNoteIndex = NOTE_NAMES.indexOf(noteName);

    let finalOctave = baseOctave + octaveChange;

    // Если текущая нота имеет меньший индекс, чем открытая струна,
    // значит мы перешли в следующую октаву
    if (currentNoteIndex < openStringIndex) {
      finalOctave += 1;
    }

    return noteName + finalOctave;
  }, []);

  /**
   * Находит позицию ноты на грифе с учетом октавы, приоритет целевой струне
   */
  const findNotePosition = useCallback((targetNote: string, preferredString?: number) => {
    // Нормализуем целевую ноту
    const normalizedTarget = normalizeNote(targetNote);

    // Если указана предпочтительная струна, ищем сначала на ней
    if (preferredString) {
      for (let fret = 0; fret <= 12; fret++) {
        const noteAtPosition = getNoteAtPosition(preferredString, fret);
        const noteWithOctave = addOctaveToNote(noteAtPosition, preferredString, fret);
        const normalizedNote = normalizeNote(noteWithOctave);

        if (normalizedNote === normalizedTarget) {
          return { string: preferredString, fret };
        }
      }
    }

    // Если не найдено на предпочтительной струне, ищем на всех остальных
    for (let string = 1; string <= 6; string++) {
      // Пропускаем предпочтительную струну, так как уже искали на ней
      if (string === preferredString) continue;

      for (let fret = 0; fret <= 12; fret++) {
        const noteAtPosition = getNoteAtPosition(string, fret);
        const noteWithOctave = addOctaveToNote(noteAtPosition, string, fret);
        const normalizedNote = normalizeNote(noteWithOctave);

        if (normalizedNote === normalizedTarget) {
          return { string, fret };
        }
      }
    }

    // Если не найдено, возвращаем позицию целевой ноты
    return { string: targetString, fret: targetFret };
  }, [getNoteAtPosition, normalizeNote, targetString, targetFret, addOctaveToNote]);

  /**
   * Вычисляет целевую ноту на основе начальной ноты и интервала
   */
  const calculateTargetNote = useCallback((startNote: string, interval: string, useSharp: boolean = true) => {
    // Преобразуем интервал в полутоны
    const semitones = INTERVALS_SEMITONES[interval] || 0;

    // Получаем базовую ноту без форматирования
    // Для этого сначала получаем ноту без форматирования из startNote
    const plainStartNote = startNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');

    // Используем функцию из musicTheory.ts для получения базовой ноты
    const targetNote = calculateTargetNoteFromTheory(plainStartNote, semitones);

    // Возвращаем ноту без форматирования, форматирование будет применено позже
    return targetNote;
  }, []);

  /**
   * Генерирует новый вопрос в зависимости от текущего режима
   */
  const generateQuestion = useCallback(() => {
    if (!isPlaying) return;

    // Сбрасываем флаг обработки ответа для нового вопроса
    isProcessingAnswerRef.current = false;

    if (isIntervalMode) {
      // Генерация вопроса для режима интервалов
      // Генерируем случайные параметры
      const startNoteIndex = getRandomInt(0, NOTE_NAMES.length - 1);
      const useSharp = Math.random() < 0.5;
      const degreesArray = Object.keys(GROUPED_INTERVALS);
      const randomDegree = degreesArray[getRandomInt(0, degreesArray.length - 1)];
      const intervalsForDegree = GROUPED_INTERVALS[randomDegree as keyof typeof GROUPED_INTERVALS];
      const targetInterval = intervalsForDegree[getRandomInt(0, intervalsForDegree.length - 1)];

      // Вычисляем ноты
      // Получаем базовую ноту без форматирования
      const baseStartNote = NOTE_NAMES[startNoteIndex];
      // Форматируем начальную ноту
      const startNote = formatNote(baseStartNote, useSharp);

      // Вычисляем целевую ноту и форматируем её
      const baseTargetNote = calculateTargetNote(baseStartNote, targetInterval, useSharp);
      const targetNote = formatNote(baseTargetNote, useSharp);

      // Выбираем случайную струну (1-6)
      const targetString = getRandomInt(1, 6);

      // Обновляем состояние через dispatch
      dispatch({
        type: 'SET_INTERVAL_STATE',
        payload: {
          startNote,
          targetInterval,
          targetNote,
          targetString,
          clickedFret: null,
          rootFret: null,
          isCorrect: null,
          animate: true,
          useSharp,
          foundRoot: false,
        }
      });

      // Отключаем анимацию через короткое время
      setTimeout(() => dispatch({
        type: 'SET_INTERVAL_STATE',
        payload: { animate: false }
      }), 300);
    } else {
      // Генерация вопроса для режима нот
      // Выбираем случайную ноту и позицию
      const randomIndex = getRandomInt(0, GUITAR_NOTES.length - 1);
      const noteEntry = GUITAR_NOTES[randomIndex];
      const [note, positionData] = noteEntry.split(':');
      const positions = positionData.split(':');
      const position = positions[getRandomInt(0, positions.length - 1)];

      // Получаем номер струны и лада
      const stringNumber = parseInt(position[0]);
      const fretNumber = parseInt(position.slice(1));

      // Обновляем состояние
      dispatch({
        type: 'SET_TARGET_NOTE',
        payload: { note, string: stringNumber, fret: fretNumber }
      });

      // Отключаем анимацию через короткое время
      setTimeout(() => dispatch({ type: 'SET_ANIMATE', payload: false }), 300);
    }
  }, [calculateTargetNote, isPlaying, isIntervalMode, getRandomInt, dispatch]);

  /**
   * Обрабатывает распознанную ноту с микрофона
   */
  const handleNoteDetected = useCallback((note: string, frequency: number) => {
    if (!isPlaying || !isMicrophoneEnabled || !note || isProcessingAnswerRef.current) {
      return;
    }

    // Нормализуем распознанную ноту
    const normalizedDetectedNote = normalizeNote(note);
    // Убираем октаву для сравнения в режиме интервалов
    const detectedNoteWithoutOctave = normalizedDetectedNote.slice(0, -1);

    if (isIntervalMode) {
      // Режим интервалов
      if (intervalState.targetString === 0) {
        return;
      }

      // Проверяем, ищем ли мы корневую ноту или целевую ноту интервала
      const isRootPhase = !intervalState.foundRoot;

      // Для корректного сравнения нужно привести ноты к одному формату
      const plainStartNote = intervalState.startNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');
      const plainTargetNote = intervalState.targetNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');

      // Убираем октавы из нот для сравнения
      const startNoteWithoutOctave = plainStartNote.length > 1 && /\d/.test(plainStartNote.slice(-1))
        ? plainStartNote.slice(0, -1)
        : plainStartNote;
      const targetNoteWithoutOctave = plainTargetNote.length > 1 && /\d/.test(plainTargetNote.slice(-1))
        ? plainTargetNote.slice(0, -1)
        : plainTargetNote;

      const expectedNote = isRootPhase ? startNoteWithoutOctave : targetNoteWithoutOctave;

      // Нормализуем обе ноты к диезам для корректного сравнения
      const normalizedDetectedNote = normalizeNote(detectedNoteWithoutOctave);
      const normalizedExpectedNote = normalizeNote(expectedNote);

      const correct = normalizedDetectedNote === normalizedExpectedNote;

      // НЕМЕДЛЕННО устанавливаем флаг, что ответ обрабатывается (для любого ответа)
      isProcessingAnswerRef.current = true;

      if (correct) {

        // Обновляем статистику
        dispatch({ type: 'UPDATE_STATS', payload: { correct: true } });

        // Обновляем состояние через dispatch
        dispatch({
          type: 'SET_INTERVAL_STATE',
          payload: {
            clickedFret: { string: intervalState.targetString, fret: 0 }, // Фрет не важен для микрофона
            rootFret: isRootPhase ? { string: intervalState.targetString, fret: 0 } : intervalState.rootFret,
            isCorrect: true,
            foundRoot: isRootPhase ? true : intervalState.foundRoot,
            targetString: isRootPhase ? Math.floor(Math.random() * 6) + 1 : intervalState.targetString,
          }
        });

        // Устанавливаем таймаут для следующего шага
        setTimeout(() => {
          if (!isRootPhase) {
            generateQuestion();
          }
        }, 500);
      } else {

        // Обновляем статистику для неверного ответа
        dispatch({ type: 'UPDATE_STATS', payload: { correct: false } });

        // Сбрасываем флаг через короткое время, чтобы можно было попробовать снова
        setTimeout(() => {
          isProcessingAnswerRef.current = false;
        }, 1000); // НЕ генерируем новый вопрос, ждем правильного ответа
      }
    } else {
      // Режим нот - сравниваем ноты С УЧЕТОМ октав
      const plainTargetNote = targetNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');

      // Нормализуем обе ноты к диезам для корректного сравнения (с октавами)
      const normalizedDetectedNoteWithOctave = normalizeNote(normalizedDetectedNote);
      const normalizedTargetNoteWithOctave = normalizeNote(plainTargetNote);

      const correct = normalizedDetectedNoteWithOctave === normalizedTargetNoteWithOctave;

      // НЕМЕДЛЕННО устанавливаем флаг, что ответ обрабатывается (для любого ответа)
      isProcessingAnswerRef.current = true;

      // Находим позицию распознанной ноты на грифе (приоритет целевой струне)
      const detectedPosition = findNotePosition(normalizedDetectedNote, targetString);

      if (correct) {
        // Обновляем статистику
        dispatch({ type: 'UPDATE_STATS', payload: { correct: true } });

        // Обновляем состояние - правильная нота отображается на целевой позиции
        dispatch({
          type: 'SET_CLICKED_FRET',
          payload: {
            string: targetString,
            fret: targetFret,
            note: normalizedDetectedNoteWithOctave,
            isCorrect: true
          }
        });

        // Устанавливаем таймаут для следующего шага
        setTimeout(() => {
          generateQuestion();
        }, 500);
      } else {
        // Обновляем статистику для неверного ответа
        dispatch({ type: 'UPDATE_STATS', payload: { correct: false } });

        // Обновляем состояние - неверная нота отображается на своей позиции
        dispatch({
          type: 'SET_CLICKED_FRET',
          payload: {
            string: detectedPosition.string,
            fret: detectedPosition.fret,
            note: normalizedDetectedNoteWithOctave,
            isCorrect: false
          }
        });

        // Сбрасываем флаг через короткое время, чтобы можно было попробовать снова
        setTimeout(() => {
          isProcessingAnswerRef.current = false;
        }, 1000); // НЕ генерируем новый вопрос, ждем правильного ответа
      }
    }
  }, [isPlaying, isMicrophoneEnabled, isIntervalMode, intervalState, targetNote, targetString, targetFret, normalizeNote, dispatch, generateQuestion]);

  // Обновляем реф с текущей версией handleNoteDetected
  handleNoteDetectedRef.current = handleNoteDetected;

  /**
   * Обрабатывает клики на ладах в зависимости от текущего режима
   */
  const handleFretClick = useCallback((stringIndex: number, fretIndex: number) => {
    if (!isPlaying || isMicrophoneEnabled) return;

    if (isIntervalMode) {
      // Режим интервалов
      if (stringIndex !== intervalState.targetString) return;

      // Получаем ноту на этой струне и ладу
      const clickedNote = getNoteAtPosition(stringIndex, fretIndex, intervalState.useSharp);

      // Проверяем, ищем ли мы корневую ноту или целевую ноту интервала
      const isRootPhase = !intervalState.foundRoot;

      // Для корректного сравнения нужно привести ноты к одному формату
      // Удаляем форматирование из нот для сравнения
      const plainClickedNote = clickedNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');
      const plainStartNote = intervalState.startNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');
      const plainTargetNote = intervalState.targetNote.replace(/[♯♭]/g, match => match === '♯' ? '#' : 'b');

      const correct = isRootPhase
        ? plainClickedNote === plainStartNote
        : plainClickedNote === plainTargetNote;

      // Обновляем состояние через dispatch
      dispatch({
        type: 'SET_INTERVAL_STATE',
        payload: {
          clickedFret: { string: stringIndex, fret: fretIndex },
          rootFret: isRootPhase && correct ? { string: stringIndex, fret: fretIndex } : intervalState.rootFret,
          isCorrect: correct,
          foundRoot: isRootPhase ? correct : intervalState.foundRoot,
          targetString: isRootPhase && correct ? Math.floor(Math.random() * 6) + 1 : intervalState.targetString,
        }
      });

      // Обновляем статистику
      dispatch({ type: 'UPDATE_STATS', payload: { correct } });

      // Устанавливаем таймаут для следующего шага
      setTimeout(() => {
        if (correct && !isRootPhase) generateQuestion();
        else if (!correct) dispatch({
          type: 'SET_INTERVAL_STATE',
          payload: { clickedFret: null }
        });
      }, 500);
    } else {
      // Режим нот
      // Проверяем, правильная ли позиция
      const correct = stringIndex === targetString && fretIndex === targetFret;

      // Находим ноту на этой струне и ладу
      const clickedNote = getNoteAtPosition(stringIndex, fretIndex);

      // Обновляем состояние
      dispatch({
        type: 'SET_CLICKED_FRET',
        payload: {
          string: stringIndex,
          fret: fretIndex,
          note: clickedNote,
          isCorrect: correct
        }
      });

      // Обновляем статистику
      dispatch({ type: 'UPDATE_STATS', payload: { correct } });

      // Устанавливаем таймаут для следующего шага
      setTimeout(() => {
        if (correct) generateQuestion();
        else dispatch({ type: 'SET_CLICKED_FRET', payload: null });
      }, 500);
    }
  }, [
    isIntervalMode,
    isPlaying,
    isMicrophoneEnabled,
    intervalState,
    targetString,
    targetFret,
    generateQuestion,
    dispatch,
    getNoteAtPosition
  ]);



  /**
   * Обрабатывает клик по кнопке старт/стоп
   * Запускает или останавливает игру и сбрасывает состояние
   */
  const handleStartStop = useCallback(() => {
    if (isPlaying) {
      // Останавливаем игру
      dispatch({ type: 'STOP_GAME' });
    } else {
      // Запускаем игру
      dispatch({ type: 'START_GAME' });

      // Генерируем первый вопрос
      generateQuestion();
    }
  }, [isPlaying, dispatch, generateQuestion]);

  /**
   * Обрабатывает изменение режима игры
   */
  const handleModeChange = useCallback((checked: boolean) => {
    dispatch({ type: 'TOGGLE_INTERVAL_MODE', payload: checked });
    // Если игра запущена, останавливаем её при смене режима
    if (isPlaying) {
      dispatch({ type: 'STOP_GAME' });
    }
  }, [isPlaying, dispatch]);

  /**
   * Обрабатывает переключение микрофона
   */
  const handleMicrophoneToggle = useCallback((checked: boolean) => {

    dispatch({ type: 'TOGGLE_MICROPHONE', payload: checked });
  }, [dispatch, isMicrophoneEnabled]);

  /**
   * Запускает микрофон для распознавания звука
   */
  const startMicrophone = useCallback(async () => {
    try {
      if (!audioAnalyzerRef.current) {
        audioAnalyzerRef.current = new AudioAnalyzer();
      }

      // Устанавливаем выбранное аудиоустройство
      if (settings.selectedDevice) {
        audioAnalyzerRef.current.setAudioDevice(settings.selectedDevice);
      }

      // Инициализируем анализатор
      const initialized = await audioAnalyzerRef.current.initialize();
      if (initialized) {

        // Устанавливаем настройки
        audioAnalyzerRef.current.setSensitivity(settings.sensitivity);
        audioAnalyzerRef.current.setNoiseThreshold(settings.noiseThreshold);

        // Запускаем анализ звука
        audioAnalyzerRef.current.startListening((note, frequency) => {
          handleNoteDetectedRef.current?.(note, frequency);
        });
      } else {
        console.error('Не удалось инициализировать микрофон');
        alert('Не удалось получить доступ к микрофону. Проверьте разрешения браузера.');
        dispatch({ type: 'TOGGLE_MICROPHONE', payload: false });
      }
    } catch (error) {
      console.error('Ошибка при инициализации аудио анализатора:', error);
      alert('Произошла ошибка при инициализации микрофона.');
      dispatch({ type: 'TOGGLE_MICROPHONE', payload: false });
    }
  }, [settings.sensitivity, settings.noiseThreshold, settings.selectedDevice, dispatch]);

  /**
   * Останавливает микрофон
   */
  const stopMicrophone = useCallback(() => {
    if (audioAnalyzerRef.current) {
      audioAnalyzerRef.current.stopListening();
      audioAnalyzerRef.current.dispose();
      audioAnalyzerRef.current = null;
    }
  }, []);



  // --- Effects ---

  // Эффект для обновления скорости и времени
  useEffect(() => {
    if (!isPlaying) return;

    const timeInterval = setInterval(() => {
      // Обновляем время и скорость в одном действии
      const notesPerMinute = score > 0 ? (score / (time + 1)) * 60 : 0;
      dispatch({
        type: 'UPDATE_STATS',
        payload: {
          time: 1,
          speed: score > 0 ? notesPerMinute : undefined
        }
      });
    }, 1000);

    return () => clearInterval(timeInterval);
  }, [isPlaying, time, score, dispatch]);



  // Эффект для управления микрофоном при изменении состояния
  useEffect(() => {
    if (isMicrophoneEnabled) {
      startMicrophone();
    } else {
      stopMicrophone();
    }
  }, [isMicrophoneEnabled, startMicrophone, stopMicrophone]);

  // Эффект для очистки микрофона при размонтировании компонента
  useEffect(() => {
    return () => {
      stopMicrophone();
    };
  }, [stopMicrophone]);

  // Эффект для генерации первой ноты или интервала при запуске игры
  useEffect(() => {
    if (!isPlaying) return;

    // Генерируем первую ноту или интервал только если они еще не заданы
    if ((isIntervalMode && !intervalState.startNote) || (!isIntervalMode && !targetNote)) {
      generateQuestion();
    }
  }, [isPlaying, isIntervalMode, targetNote, intervalState.startNote, generateQuestion]);

  // --- Memoized values ---

  // Создаем функцию для создания объекта ноты
  const createNoteObject = useCallback((
    id: string,
    string: number,
    fret: number,
    label: string,
    isCorrect?: boolean | undefined,
    isRoot?: boolean
  ): NoteDisplay => ({
    id,
    string,
    fret,
    label,
    isCorrect,
    isRoot
  }), []);

  // Подготавливаем ноты для отображения на грифе в режиме нот
  const fretboardNotes = useMemo(() => {
    const notes: NoteDisplay[] = [];

    // Добавляем только кликнутую/распознанную ноту (зеленая/красная)
    // Целевая нота НЕ отображается на грифе - пользователь должен найти её сам
    if (clickedFret) {
      notes.push(createNoteObject(
        `clicked-${clickedFret.string}-${clickedFret.fret}`,
        clickedFret.string,
        clickedFret.fret,
        state.noteGame.clickedNote,
        state.noteGame.isCorrect === null ? undefined : state.noteGame.isCorrect
      ));
    }

    return notes;
  }, [clickedFret, state.noteGame.clickedNote, state.noteGame.isCorrect, createNoteObject]);

  // Подготавливаем ноты для отображения на грифе в режиме интервалов
  const intervalFretboardNotes = useMemo(() => {
    const notes: NoteDisplay[] = [];

    // Добавляем кликнутую ноту
    if (intervalState.clickedFret) {
      notes.push(createNoteObject(
        `clicked-${intervalState.clickedFret.string}-${intervalState.clickedFret.fret}`,
        intervalState.clickedFret.string,
        intervalState.clickedFret.fret,
        intervalState.foundRoot ? intervalState.targetNote : intervalState.startNote,
        intervalState.isCorrect === null ? undefined : intervalState.isCorrect
      ));
    }

    // Добавляем корневую ноту, если она найдена
    if (intervalState.rootFret) {
      notes.push(createNoteObject(
        `root-${intervalState.rootFret.string}-${intervalState.rootFret.fret}`,
        intervalState.rootFret.string,
        intervalState.rootFret.fret,
        intervalState.startNote,
        undefined,
        true
      ));
    }

    return notes;
  }, [intervalState, createNoteObject]);

  // Мемоизированный компонент статистики
  const Statistics = useMemo(() => (
    <div className="flex justify-between items-center p-3 rounded-lg shadow-sm mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 w-full max-w-md">
      {/* Счет */}
      <div className="flex items-center">
        <span className="text-gray-500 dark:text-gray-400 mr-0.5 font-medium text-sm">Счет:</span>
        <span className="inline-flex items-center justify-center px-1.5 py-0.5 font-bold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100 text-sm w-[2rem] text-center">{score}</span>
        <span className="mx-0.5 font-bold text-gray-500 dark:text-gray-400">:</span>
        <span className="inline-flex items-center justify-center px-1.5 py-0.5 font-bold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 text-sm w-[2rem] text-center">{total - score}</span>
      </div>

      {/* Точность */}
      <div className="flex items-center ml-1">
        <span className="inline-flex items-center justify-center px-1.5 py-0.5 font-bold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 text-sm w-[3rem] text-center">{accuracy}%</span>
      </div>

      {/* Скорость */}
      <div className="flex items-center ml-1">
        <span className="inline-block w-4 h-4 text-gray-500 dark:text-gray-400 mr-0.5">⚡</span>
        <span className="inline-flex items-center justify-center px-1.5 py-0.5 font-bold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 text-sm w-[2rem] text-center">{Math.round(speed)}</span>
        <span className="ml-0.5 text-gray-500 dark:text-gray-400 text-xs">нот/мин</span>
      </div>

      {/* Время */}
      <div className="flex items-center ml-1">
        <span className="inline-block w-4 h-4 text-gray-500 dark:text-gray-400 mr-0.5">⏱️</span>
        <span className="inline-flex items-center justify-center px-1.5 py-0.5 font-bold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 text-sm w-[3.5rem] text-center font-mono">
          {Math.floor(time / 60)}:{(time % 60).toString().padStart(2, '0')}
        </span>
      </div>
    </div>
  ), [score, total, accuracy, speed, time]);

  // Компонент отображения ноты в режиме интервалов
  const renderIntervalNoteDisplay = () => (
    <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 relative">
      <div
        className={`absolute inset-0 rounded-lg ${
          intervalState.clickedFret
            ? `bg-${intervalState.isCorrect ? 'green' : 'red'}-600`
            : 'bg-yellow-400'
        } ${intervalState.clickedFret ? `pulse-${intervalState.isCorrect ? 'green' : 'red'}` : ''} ${
          intervalState.animate ? 'pop-up' : ''
        }`}
      ></div>
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <span
          className="font-bold leading-none p-1 text-center text-yellow-50"
          style={{ fontSize: 'clamp(1.2rem, 3vw, 2rem)' }}
        >
          {formatNote(intervalState.startNote, intervalState.useSharp)}
        </span>
        <span
          className="font-bold leading-none p-1 text-center text-yellow-50"
          style={{ fontSize: 'clamp(0.8rem, 2vw, 1.5rem)' }}
        >
          1 {intervalState.targetInterval}
        </span>
      </div>
    </div>
  );

  // Компонент отображения ноты в режиме нот
  const renderNoteDisplay = () => (
    <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 relative">
      <div
        className={`absolute inset-0 rounded-lg ${
          clickedFret
            ? `bg-${isCorrect ? 'green' : 'red'}-600`
            : 'bg-yellow-400'
        } ${clickedFret ? `pulse-${isCorrect ? 'green' : 'red'}` : ''} ${
          animate ? 'pop-up' : ''
        }`}
      ></div>
      <div className="absolute inset-0 flex items-center justify-center">
        <span
          className="font-bold leading-none p-1 text-center text-yellow-50"
          style={{ fontSize: 'clamp(1.5rem, 4vw, 2.5rem)' }}
        >
          {formatNote(targetNote)}
        </span>
      </div>
    </div>
  );

  // Компонент переключателя режимов
  const renderModeSwitch = () => (
    <div className="flex items-center justify-center mt-4 mb-2">
      <span className={`mr-2 ${!isIntervalMode ? 'font-bold' : ''} text-gray-700 dark:text-gray-300`}>Ноты</span>
      <Switch
        checked={isIntervalMode}
        onCheckedChange={handleModeChange}
        className="mx-2"
      />
      <span className={`ml-2 ${isIntervalMode ? 'font-bold' : ''} text-gray-700 dark:text-gray-300`}>Интервалы</span>
    </div>
  );

  // Компонент переключателя микрофона
  const renderMicrophoneSwitch = () => (
    <div className="flex items-center justify-center mt-2 mb-2">
      <span className="mr-2 text-gray-700 dark:text-gray-300">🎸</span>
      <Switch
        checked={isMicrophoneEnabled}
        onCheckedChange={handleMicrophoneToggle}
        className="mx-2"
      />
      <span className="ml-2 text-gray-700 dark:text-gray-300">🎤</span>
    </div>
  );

  // Компонент инструкций
  const renderInstructions = () => {
    if (!isPlaying) return null;

    if (isIntervalMode) {
      const isRootPhase = !intervalState.foundRoot;
      const targetNoteName = isRootPhase ? intervalState.startNote : intervalState.targetNote;

      if (isMicrophoneEnabled) {
        return (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Сыграйте ноту <strong>{formatNote(targetNoteName, intervalState.useSharp)}</strong> на выделенной струне
            </p>
          </div>
        );
      } else {
        return (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Найдите ноту <strong>{formatNote(targetNoteName, intervalState.useSharp)}</strong> на выделенной струне
            </p>
          </div>
        );
      }
    } else {
      if (isMicrophoneEnabled) {
        return (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Сыграйте ноту <strong>{formatNote(targetNote)}</strong> на выделенной струне
            </p>
          </div>
        );
      } else {
        return (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Найдите ноту <strong>{formatNote(targetNote)}</strong> на выделенной струне
            </p>
          </div>
        );
      }
    }
  };

  // --- Render ---
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4 font-sans relative">
      {/* Back Button - перемещена правее, чтобы не перекрывать гамбургер-меню */}
      <BackButton className="absolute top-4 left-16" />

      {/* Game Title */}
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-center mb-6 md:mb-8 text-gray-900 dark:text-white">
        {isIntervalMode ? "Интервалы на грифе" : "Найди ноту"}
      </h1>

      {/* Statistics */}
      {Statistics}

      <div className="w-full max-w-4xl mx-auto">
        {/* Target Note Display */}
        <div className="flex justify-center mb-6 md:mb-8">
          {isIntervalMode ? renderIntervalNoteDisplay() : renderNoteDisplay()}
        </div>

        {/* Instructions */}
        {renderInstructions()}

        {/* Используем общий компонент грифа */}
        <div className="relative mb-4 md:mb-6">
          <GuitarFretboard
            highlightedString={isIntervalMode ? intervalState.targetString : targetString}
            animate={isIntervalMode ? intervalState.animate : animate}
            notes={isIntervalMode ? intervalFretboardNotes : fretboardNotes}
            onFretClick={isPlaying && !isMicrophoneEnabled ? handleFretClick : undefined}
          />
        </div>

        {/* Game Controls and Statistics */}
        <div className="text-center space-y-4 md:space-y-5 mb-4">
          {/* Start/Stop Button */}
          <button
            onClick={handleStartStop}
            className={`px-4 py-2 rounded-md font-medium ${isPlaying ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'}`}
          >
            {isPlaying ? 'Стоп' : 'Старт'}
          </button>

          {/* Mode Switch */}
          {renderModeSwitch()}

          {/* Microphone Switch */}
          {renderMicrophoneSwitch()}
        </div>
      </div>
    </div>
  );
};

export default GuitarNoteIntervalGame;
