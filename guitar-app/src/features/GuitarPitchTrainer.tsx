"use client"

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { Button } from "../shared/ui/button"
import { Label } from "../shared/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../shared/ui/dialog"
import { Setting<PERSON>, RefreshCw, Shuffle } from 'lucide-react'
import { Slider } from "../shared/ui/slider"
import { Select } from "../shared/ui/select"
import { Switch } from "../shared/ui/switch"
import { Scale, scalePatterns } from '../constants/musicTheory'
import BackButton from "../shared/ui/back-button"
import GuitarFretboard, { NoteDisplay } from '../shared/components/GuitarFretboard'
// Импортируем AudioAnalyzer для распознавания звука
import { AudioAnalyzer } from '../shared/utils/AudioAnalyzer'
// Импортируем контекст аудио настроек
import { useAudioSettings } from '../shared/contexts/AudioSettingsContext'

// Маппинги для сложности
const DIFFICULTY_MAP = { '1 нота': 1, '2 ноты': 2, '3 ноты': 3, '4 ноты': 4, '5 нот': 5 } as const
const DIFFICULTY_REVERSE_MAP = { 1: '1 нота', 2: '2 ноты', 3: '3 ноты', 4: '4 ноты', 5: '5 нот' } as const

type NoteShape = 'rectangle' | 'triangle-up' | 'triangle-down'
type DifficultyLevel = '1 нота' | '2 ноты' | '3 ноты' | '4 ноты' | '5 нот'
type GameMode = 'standard' | 'repeat' | 'sing'
type StringSelectionMode = 'random' | 'ascending' | 'descending'

interface Note {
  note: string
  color: string
  shape: NoteShape
  midiOffset: number
  isDisabled?: boolean
}

interface GameState {
  key: string
  scale: Scale
  isRandomKey: boolean
  isRandomScale: boolean
  autoPlay: boolean
  showNote: boolean
  isPlaying: boolean
  correct: number
  incorrect: number
  speed: number
  isBassEnabled: boolean
  repeatOnError: boolean
  muteBtnSound: boolean
  difficultyLevel: DifficultyLevel
  gameMode: GameMode
  tempo: number
  showNotesAsSteps: boolean
  stringSelectionMode: StringSelectionMode
  isMicrophoneEnabled: boolean
}

const initialState: GameState = {
  key: 'C',
  scale: 'Natural Major',
  isRandomKey: false,
  isRandomScale: false,
  autoPlay: false,
  showNote: false,
  isPlaying: false,
  correct: 0,
  incorrect: 0,
  speed: 0,
  isBassEnabled: true,
  repeatOnError: false,
  muteBtnSound: true,
  difficultyLevel: '1 нота',
  gameMode: 'standard',
  tempo: 60,
  showNotesAsSteps: true,
  stringSelectionMode: 'random',
  isMicrophoneEnabled: false,
}

const allNotes: Note[] = [
  { note: '#4', color: 'bg-[#FF2400]', shape: 'triangle-up', midiOffset: 6 },
  { note: '4', color: 'bg-[#7FFFD4]', shape: 'triangle-down', midiOffset: 5 },
  { note: '3', color: 'bg-[#FFA500]', shape: 'triangle-up', midiOffset: 4 },
  { note: 'b3', color: 'bg-[#0000FF]', shape: 'triangle-down', midiOffset: 3 },
  { note: '2', color: 'bg-[#FAFA33]', shape: 'triangle-up', midiOffset: 2 },
  { note: 'b2', color: 'bg-[#8A2BE2]', shape: 'triangle-down', midiOffset: 1 },
  { note: '1', color: 'bg-[#008000]', shape: 'rectangle', midiOffset: 0 },
  { note: '7', color: 'bg-[#FF0000]', shape: 'triangle-up', midiOffset: -1 },
  { note: 'b7', color: 'bg-[#87CEEB]', shape: 'triangle-down', midiOffset: -2 },
  { note: '6', color: 'bg-[#FFFF00]', shape: 'triangle-up', midiOffset: -3 },
  { note: 'b6', color: 'bg-[#8A2BE2]', shape: 'triangle-down', midiOffset: -4 },
  { note: '5', color: 'bg-[#9ACD32]', shape: 'rectangle', midiOffset: 7 },
]

const noteMidiNumbers: { [key: string]: number } = {
  'C': 60, 'C#/Db': 61, 'D': 62, 'D#/Eb': 63, 'E': 64, 'F': 65,
  'F#/Gb': 66, 'G': 67, 'G#/Ab': 68, 'A': 69, 'A#/Bb': 70, 'B': 71
}



// Функция для получения MIDI номера открытой струны
const getOpenStringMidi = (stringNumber: number): number => {
  // Стандартный строй гитары (от 1-й к 6-й струне)
  // 1-я струна (самая тонкая) = E4, 6-я струна (самая толстая) = E2
  const midiValues = [64, 59, 55, 50, 45, 40] // E4, B3, G3, D3, A2, E2

  return midiValues[stringNumber - 1] || 64
}

// Утилитарные функции
const midiToFrequency = (midiNote: number) => 440 * Math.pow(2, (midiNote - 69) / 12)
const calculatePercentage = (value: number, total: number) => total ? Math.round((value / total) * 100) : 0

// Константы для оптимизации
const NOTE_NAMES = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'] as const

// Аудио хук с исправленными утечками памяти
const useAudio = () => {
  const audioContextRef = useRef<AudioContext | null>(null)
  const bassOscillatorRef = useRef<OscillatorNode | null>(null)
  const bassGainRef = useRef<GainNode | null>(null)
  const activeOscillatorsRef = useRef<Set<OscillatorNode>>(new Set())

  const getAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    }
    // Активируем контекст, если он приостановлен
    if (audioContextRef.current.state === 'suspended') {
      audioContextRef.current.resume()
    }
    return audioContextRef.current
  }, [])

  // Очистка всех активных осцилляторов
  const cleanupOscillators = useCallback(() => {
    activeOscillatorsRef.current.forEach(osc => {
      try {
        osc.stop()
        osc.disconnect()
      } catch (e) {
        // Игнорируем ошибки при остановке уже остановленных осцилляторов
      }
    })
    activeOscillatorsRef.current.clear()
  }, [])

  // Очистка аудио контекста
  const cleanup = useCallback(() => {
    cleanupOscillators()
    if (bassOscillatorRef.current) {
      try {
        bassOscillatorRef.current.stop()
        bassOscillatorRef.current.disconnect()
      } catch (e) {}
      bassOscillatorRef.current = null
    }
    if (bassGainRef.current) {
      bassGainRef.current.disconnect()
      bassGainRef.current = null
    }
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close()
      audioContextRef.current = null
    }
  }, [cleanupOscillators])

  const playNote = useCallback((midiNote: number, isBass = false) => {
    try {
      const context = getAudioContext()
      const osc = context.createOscillator()
      const gain = context.createGain()
      osc.type = 'sine'
      osc.frequency.setValueAtTime(midiToFrequency(midiNote), context.currentTime)

      if (isBass) {
        if (bassOscillatorRef.current) {
          try {
            bassOscillatorRef.current.stop()
            bassOscillatorRef.current.disconnect()
          } catch (e) {}
          bassGainRef.current?.disconnect()
          bassOscillatorRef.current = null
          bassGainRef.current = null
        }
        bassOscillatorRef.current = osc
        bassGainRef.current = gain
        gain.gain.setValueAtTime(0, context.currentTime)
        gain.gain.linearRampToValueAtTime(0.2, context.currentTime + 2)
        osc.start()
        
        // Автоматическая очистка для баса
        osc.onended = () => {
          osc.disconnect()
          gain.disconnect()
          if (bassOscillatorRef.current === osc) {
            bassOscillatorRef.current = null
            bassGainRef.current = null
          }
        }
      } else {
        // Добавляем в активные осцилляторы
        activeOscillatorsRef.current.add(osc)
        
        gain.gain.setValueAtTime(0.7, context.currentTime)
        gain.gain.exponentialRampToValueAtTime(0.001, context.currentTime + 1.5)
        osc.start()
        osc.stop(context.currentTime + 1.5)
        
        // Автоматическая очистка для обычных нот
        osc.onended = () => {
          osc.disconnect()
          gain.disconnect()
          activeOscillatorsRef.current.delete(osc)
        }
      }

      osc.connect(gain).connect(context.destination)
    } catch (error) {
      console.error('Error playing note:', error)
    }
  }, [getAudioContext])

  const startBass = useCallback((key: string) => {
    playNote(noteMidiNumbers[key] - 12, true) // Бас на октаву ниже корня тональности
  }, [playNote])

  const stopBass = useCallback(() => {
    if (bassOscillatorRef.current && bassGainRef.current) {
      const context = getAudioContext();
      const stopTime = context.currentTime + 2;
      bassGainRef.current.gain.linearRampToValueAtTime(0, stopTime);
      bassOscillatorRef.current.stop(stopTime);
    }
  }, [getAudioContext]);

  const updateBassFreq = useCallback((key: string) => {
    if (bassOscillatorRef.current) {
      bassOscillatorRef.current.frequency.setValueAtTime(
        midiToFrequency(noteMidiNumbers[key] - 12), // Бас на октаву ниже корня тональности
        getAudioContext().currentTime
      )
    }
  }, [getAudioContext])

  // Очистка ресурсов при размонтировании
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return { playNote, startBass, stopBass, updateBassFreq, cleanup };
}

const getInitialState = (): GameState => {
  try {
    // Пытаемся загрузить последние использованные параметры
    const lastParams = localStorage.getItem('pitchTrainerLastParams');
    const { key, scale, difficultyLevel, gameMode, stringSelectionMode } = lastParams ? JSON.parse(lastParams) : initialState;

    const storageKey = `pitchTrainerSettings-${gameMode}-${key}-${scale}-${difficultyLevel}`;
    const savedSettings = localStorage.getItem(storageKey);

    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      // Собираем состояние: сначала initialState, потом последние параметры, потом сохраненные настройки для этих параметров
      return { ...initialState, key, scale, difficultyLevel, gameMode, stringSelectionMode: stringSelectionMode || 'random', ...parsedSettings };
    }
    // Если для последних параметров нет настроек, просто возвращаем их с initialState
    return { ...initialState, key, scale, difficultyLevel, gameMode, stringSelectionMode: stringSelectionMode || 'random' };
  } catch (error) {
    console.error("Error reading from localStorage", error);
  }
  return initialState;
};

const GuitarPitchTrainer: React.FC = () => {
  const [state, setState] = useState<GameState>(getInitialState());
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [targetSequence, setTargetSequence] = useState<Note[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)

  const [isPlayback, setIsPlayback] = useState(false)
  const [gameStartTime, setGameStartTime] = useState<number | null>(null)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [currentFretboardPosition, setCurrentFretboardPosition] = useState<{ string: number, fret: number } | null>(null)
  const [selectedString, setSelectedString] = useState<number>(0) // 0 = нет выделенной струны
  const [noteStatus, setNoteStatus] = useState<{ [key: string]: boolean | null }>({}) // Статус нот для анимации

  const timeoutRefs = useRef<NodeJS.Timeout[]>([])
  const startTimeRef = useRef<number | null>(null)
  const currentStringIndexRef = useRef<number>(0) // Для последовательного выбора струн
  const audio = useAudio()
  const audioRef = useRef(audio)

  // Аудио анализатор и настройки для микрофона
  const audioAnalyzerRef = useRef<AudioAnalyzer | null>(null)
  const { settings } = useAudioSettings()

  // Реф для предотвращения множественных срабатываний
  const isProcessingAnswerRef = useRef<boolean>(false)

  // Реф для стабильной ссылки на handleNoteDetected
  const handleNoteDetectedRef = useRef<(note: string, frequency: number) => void>(() => {})

  // Обновляем ref при изменении audio и очищаем ресурсы при размонтировании
  useEffect(() => {
    audioRef.current = audio
  }, [audio])

  // Очистка ресурсов при размонтировании
  useEffect(() => {
    return () => {
      // Очищаем аудио ресурсы при размонтировании компонента
      audioRef.current.cleanup()
    }
  }, [])

  // Форматирование времени в формат MM:SS
  const formatTime = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }, [])

  /**
   * Нормализует ноту (бемоли в диезы) для корректного сравнения
   */
  const normalizeNote = useCallback((note: string): string => {
    if (!note) return note

    // Проверяем, есть ли октава в конце (цифра)
    const hasOctave = /\d$/.test(note)

    if (hasOctave) {
      // Нота с октавой (например, "Ab2")
      const noteName = note.slice(0, -1) // Имя ноты без октавы
      const octave = note.slice(-1) // Октава

      const flatToSharpMap: { [key: string]: string } = {
        'Ab': 'G#',
        'Bb': 'A#',
        'Db': 'C#',
        'Eb': 'D#',
        'Gb': 'F#',
      }

      if (flatToSharpMap[noteName]) {
        return flatToSharpMap[noteName] + octave
      }
    } else {
      // Нота без октавы (например, "Ab")
      const flatToSharpMap: { [key: string]: string } = {
        'Ab': 'G#',
        'Bb': 'A#',
        'Db': 'C#',
        'Eb': 'D#',
        'Gb': 'F#',
      }

      if (flatToSharpMap[note]) {
        return flatToSharpMap[note]
      }
    }

    // Дополнительно заменяем символ ♯ на #
    return note.replace('♯', '#')
  }, [])

  // Выбор струны в зависимости от режима
  const selectString = useCallback(() => {
    switch (state.stringSelectionMode) {
      case 'random':
        return Math.floor(Math.random() * 6) + 1 // Струны 1-6
      case 'ascending':
        // 6->5->4->3->2->1 (от толстой к тонкой)
        const currentAscending = 6 - (currentStringIndexRef.current % 6)
        currentStringIndexRef.current = (currentStringIndexRef.current + 1) % 6
        return currentAscending
      case 'descending':
        // 1->2->3->4->5->6 (от тонкой к толстой)
        const currentDescending = (currentStringIndexRef.current % 6) + 1
        currentStringIndexRef.current = (currentStringIndexRef.current + 1) % 6
        return currentDescending
      default:
        return Math.floor(Math.random() * 6) + 1
    }
  }, [state.stringSelectionMode])

  // Получение всех нот гаммы на конкретной струне (мемоизировано)
  const getScaleNotesOnString = useCallback((stringNumber: number, key: string, scale: Scale) => {
    const scalePattern = scalePatterns[scale]
    if (!scalePattern) return []

    const openStringMidi = getOpenStringMidi(stringNumber)
    const rootMidi = noteMidiNumbers[key]
    const availableNotes: { note: string, fret: number, midiOffset: number, actualNote: string }[] = []

    // Проверяем каждый лад на струне (0-12)
    for (let fret = 0; fret <= 12; fret++) {
      const fretMidi = openStringMidi + fret

      // Вычисляем реальную ноту на этом ладу используя константу NOTE_NAMES
      const actualNote = NOTE_NAMES[fretMidi % 12]

      // Вычисляем интервал от корня тональности
      const offsetFromRoot = (fretMidi - rootMidi) % 12
      const normalizedOffset = offsetFromRoot < 0 ? offsetFromRoot + 12 : offsetFromRoot

      // Находим соответствующую ноту в allNotes по интервалу
      const matchingNote = allNotes.find(note => {
        const noteOffset = note.midiOffset % 12
        const normalizedNoteOffset = noteOffset < 0 ? noteOffset + 12 : noteOffset
        return normalizedNoteOffset === normalizedOffset
      })

      if (matchingNote && scalePattern.includes(matchingNote.note)) {
        availableNotes.push({
          note: matchingNote.note, // Интервальное обозначение (1, 2, 3, etc.)
          fret: fret,
          midiOffset: fretMidi - rootMidi, // Реальный offset от корня
          actualNote: actualNote // Реальная нота (C, D, E, etc.)
        })
      }
    }

    return availableNotes
  }, [])

  // Генерация случайной ноты из доступных на струне
  const generateRandomNoteOnString = useCallback((stringNumber: number) => {
    const availableNotes = getScaleNotesOnString(stringNumber, state.key, state.scale)

    if (availableNotes.length === 0) {
      return { note: '1', fret: 0, midiOffset: 0, actualNote: 'C' }
    }

    const selectedNote = availableNotes[Math.floor(Math.random() * availableNotes.length)]
    return selectedNote
  }, [state.key, state.scale, getScaleNotesOnString])





  // Генерация последовательности нот
  const generateTargetSequence = useCallback(() => {
    // Сбрасываем флаг обработки ответа для нового вопроса
    isProcessingAnswerRef.current = false

    // Выбираем струну в зависимости от режима
    const newSelectedString = selectString()
    setSelectedString(newSelectedString)

    const sequenceLength = DIFFICULTY_MAP[state.difficultyLevel]

    // Генерируем последовательность только из нот на выбранной струне
    const newSequence: Note[] = Array.from({ length: sequenceLength }, () => {
      const noteOnString = generateRandomNoteOnString(newSelectedString)
      return {
        note: noteOnString.note,
        color: allNotes.find(n => n.note === noteOnString.note)?.color || 'bg-gray-400',
        shape: allNotes.find(n => n.note === noteOnString.note)?.shape || 'rectangle',
        midiOffset: noteOnString.midiOffset,
        fret: noteOnString.fret,
        actualNote: noteOnString.actualNote
      } as Note & { fret: number, actualNote: string }
    })

    setTargetSequence(newSequence)
    setCurrentIndex(0)

    // Сбрасываем статус всех нот при генерации новой последовательности
    setNoteStatus({})

    return newSequence
  }, [state.difficultyLevel, selectString, generateRandomNoteOnString])

  // Конвертация ноты в позицию на грифе
  const noteToFretboardPosition = useCallback((note: Note): { string: number, fret: number } => {
    if (!note) {
      console.error('noteToFretboardPosition called with undefined note')
      return { string: 1, fret: 0 }
    }
    const rootMidi = noteMidiNumbers[state.key]
    const targetMidi = rootMidi + note.midiOffset

    // Ищем позицию на грифе для этой MIDI ноты
    for (let string = 1; string <= 6; string++) {
      const openStringMidi = getOpenStringMidi(string)
      for (let fret = 0; fret <= 12; fret++) {
        if (openStringMidi + fret === targetMidi) {
          return { string, fret }
        }
      }
    }

    // Если не найдено, возвращаем первую струну, нулевой лад
    return { string: 1, fret: 0 }
  }, [state.key])

  // Воспроизведение последовательности
  const playSequence = useCallback((sequence: Note[], withSound: boolean) => {
    setIsPlayback(true)
    const beatDuration = (60 / state.tempo) * 1000
    const barDuration = beatDuration * 4

    if (!startTimeRef.current) {
      startTimeRef.current = Date.now()
    }

    const playNote = (note: Note, index: number) => {
      const timeout1 = setTimeout(() => {
        // Вычисляем позицию ноты на грифе
          const position = noteToFretboardPosition(note)
          setCurrentFretboardPosition(position)

        if (withSound) {
          const midiNote = noteMidiNumbers[state.key] + note.midiOffset + 12 // +12 полутонов (октава выше)
          audio.playNote(midiNote)
        }
      }, index * beatDuration)

      const timeout2 = setTimeout(() => {
        setCurrentFretboardPosition(null)
      }, (index + 1) * beatDuration)

      timeoutRefs.current.push(timeout1, timeout2)
    }

    sequence.forEach((note, index) => {
      playNote(note, index)
    })

    const pauseDuration = barDuration - (sequence.length * beatDuration)
    if (pauseDuration > 0) {
      const pauseTimeout = setTimeout(() => {
        // Пауза после последовательности
      }, sequence.length * beatDuration + pauseDuration)

      timeoutRefs.current.push(pauseTimeout)
    }

    const nextActionTimeout = setTimeout(() => {
      setIsPlayback(false)
      if (state.gameMode === 'repeat') {
        const repeatTimeout = setTimeout(() => {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, true)
        }, barDuration)
        timeoutRefs.current.push(repeatTimeout)
      } else if (state.gameMode === 'sing') {
        if (!withSound) {
          const singTimeout = setTimeout(() => {
            playSequence(sequence, true)
          }, barDuration)
          timeoutRefs.current.push(singTimeout)
        } else {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, false)
        }
      } else if (state.autoPlay) {
        const newSequence = generateTargetSequence()
        playSequence(newSequence, true)
      }
    }, barDuration)
    timeoutRefs.current.push(nextActionTimeout)
    if (state.gameMode === 'repeat' || (state.gameMode === 'sing' && !withSound)) {
      const emptyMeasureTimeout = setTimeout(() => {}, barDuration)
      timeoutRefs.current.push(emptyMeasureTimeout)
    }

  }, [state, audio, generateTargetSequence, noteToFretboardPosition])

  // Повтор последовательности
  const replaySequence = useCallback(() => {
    if (targetSequence.length === 0) return
    playSequence(targetSequence, true)
  }, [targetSequence, playSequence])

  // Обработчик старт/стоп игры
  const handleStart = useCallback(() => {
    if (state.isPlaying) {
      setState(prev => ({ ...prev, isPlaying: false }))
      setTargetSequence([])
      setCurrentIndex(0)
      setIsPlayback(false)
      setCurrentFretboardPosition(null)
      setSelectedString(0) // Убираем выделение струны
      currentStringIndexRef.current = 0 // Сбрасываем индекс для последовательного выбора
      audio.stopBass()
      timeoutRefs.current.forEach(clearTimeout)
      timeoutRefs.current = []
    } else {
      const newKey = state.isRandomKey ? Object.keys(noteMidiNumbers)[Math.floor(Math.random() * 12)] : state.key
      const newScale = state.isRandomScale ? Object.keys(scalePatterns)[Math.floor(Math.random() * Object.keys(scalePatterns).length)] as Scale : state.scale

      setState(prev => ({
        ...prev,
        isPlaying: true,
        correct: 0,
        incorrect: 0,
        key: newKey,
        scale: newScale,
        showNote: prev.gameMode === 'sing' ? true : prev.showNote,
        tempo: prev.tempo,
        speed: 0
      }))
      setGameStartTime(Date.now())
      setElapsedTime(0)

      if (state.isBassEnabled) {
        audio.startBass(newKey)
        audio.updateBassFreq(newKey)
      }

      const zeroMeasureDelay = (60 / state.tempo) * 1000 * 4
      const startTimeout = setTimeout(() => {
        const newSequence = generateTargetSequence()
        playSequence(newSequence, state.gameMode !== 'sing')
      }, zeroMeasureDelay)
      timeoutRefs.current.push(startTimeout)
    }
  }, [state, generateTargetSequence, playSequence, audio])

  /**
   * Обрабатывает распознанную ноту с микрофона
   */
  const handleNoteDetected = useCallback((note: string, _frequency: number) => {
    if (!state.isPlaying || !state.isMicrophoneEnabled || !note || isProcessingAnswerRef.current) {
      return
    }

    // Проверяем, что игра не в режиме автопроигрывания или пения
    if (state.gameMode === 'sing' || state.autoPlay || targetSequence.length === 0) {
      return
    }

    // Проверяем, что есть выбранная струна
    if (selectedString === 0) {
      console.log('🎵 [MIC] Пропуск - нет выбранной струны')
      return
    }

    // Нормализуем распознанную ноту
    const normalizedDetectedNote = normalizeNote(note)

    // Получаем текущую целевую ноту
    const targetNote = targetSequence[currentIndex] as Note & { fret: number, actualNote: string }
    if (!targetNote) {
      console.log('🎵 [MIC] Нет целевой ноты')
      return
    }

    // Вычисляем MIDI ноту для целевой позиции
    const openStringMidi = getOpenStringMidi(selectedString)
    const targetMidi = openStringMidi + targetNote.fret

    // Преобразуем MIDI в название ноты для сравнения
    const targetNoteName = NOTE_NAMES[targetMidi % 12]
    // Правильная формула для октавы в MIDI: C4 = 60, поэтому октава = floor((midi - 12) / 12)
    const targetOctave = Math.floor((targetMidi - 12) / 12)
    const targetNoteWithOctave = targetNoteName + targetOctave

    // Нормализуем целевую ноту
    const normalizedTargetNote = normalizeNote(targetNoteWithOctave)

    // Сравниваем ноты
    const correct = normalizedDetectedNote === normalizedTargetNote

    // Определяем позицию для анимации
    let animationNoteKey: string

    if (correct) {
      // Если правильно - показываем анимацию на целевой ноте
      animationNoteKey = `${selectedString}-${targetNote.fret}`
    } else {
      // Если неправильно - пытаемся найти фактическую позицию распознанной ноты на выбранной струне
      const detectedNoteName = normalizedDetectedNote.replace(/\d+$/, '') // Убираем октаву
      const detectedOctave = parseInt(normalizedDetectedNote.match(/\d+$/)?.[0] || '4')

      // Вычисляем MIDI номер распознанной ноты (правильная формула MIDI)
      const noteIndex = NOTE_NAMES.indexOf(detectedNoteName as any)
      if (noteIndex !== -1) {
        // Правильная формула: C4 = 60, поэтому MIDI = noteIndex + (octave + 1) * 12
        const detectedMidi = noteIndex + ((detectedOctave + 1) * 12)
        const openStringMidi = getOpenStringMidi(selectedString)
        const detectedFret = detectedMidi - openStringMidi

        // Если лад в разумных пределах (0-12), используем его, иначе показываем на целевой ноте
        if (detectedFret >= 0 && detectedFret <= 12) {
          animationNoteKey = `${selectedString}-${detectedFret}`
        } else {
          animationNoteKey = `${selectedString}-${targetNote.fret}`
        }
      } else {
        // Если не удалось распознать ноту, показываем на целевой позиции
        animationNoteKey = `${selectedString}-${targetNote.fret}`
      }
    }

    setNoteStatus(prev => ({ ...prev, [animationNoteKey]: correct }))

    // Сбрасываем статус через время анимации
    setTimeout(() => {
      setNoteStatus(prev => ({ ...prev, [animationNoteKey]: null }))
    }, 800)

    // НЕМЕДЛЕННО устанавливаем флаг, что ответ обрабатывается
    isProcessingAnswerRef.current = true

    if (correct) {
      setCurrentIndex(prev => prev + 1)
      setState(prev => ({ ...prev, correct: prev.correct + 1 }))

      if (currentIndex === targetSequence.length - 1) {
        // Последняя нота в последовательности - генерируем новую
        const beatDuration = (60 / state.tempo) * 1000
        const nextSequenceTimeout = setTimeout(() => {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, true)
        }, beatDuration)
        timeoutRefs.current.push(nextSequenceTimeout)
      }

      // Сбрасываем флаг через короткое время для следующей ноты
      setTimeout(() => {
        isProcessingAnswerRef.current = false
      }, 500)
    } else {
      setState(prev => ({ ...prev, incorrect: prev.incorrect + 1 }))

      if (state.repeatOnError) {
        const beatDuration = (60 / state.tempo) * 1000
        const repeatTimeout = setTimeout(() => {
          playSequence(targetSequence, true)
        }, beatDuration)
        timeoutRefs.current.push(repeatTimeout)
      }

      // Сбрасываем флаг через короткое время, чтобы можно было попробовать снова
      setTimeout(() => {
        isProcessingAnswerRef.current = false
      }, 1000)
    }
  }, [
    state.isPlaying,
    state.isMicrophoneEnabled,
    state.gameMode,
    state.autoPlay,
    state.tempo,
    state.repeatOnError,
    targetSequence,
    currentIndex,
    selectedString,
    normalizeNote,
    generateTargetSequence,
    playSequence
  ])

  // Обновляем реф с текущей версией handleNoteDetected
  handleNoteDetectedRef.current = handleNoteDetected

  /**
   * Обрабатывает переключение микрофона
   */
  const handleMicrophoneToggle = useCallback((checked: boolean) => {
    setState(prev => ({ ...prev, isMicrophoneEnabled: checked }))
  }, [])

  /**
   * Запускает микрофон для распознавания звука
   */
  const startMicrophone = useCallback(async () => {
    try {
      if (!audioAnalyzerRef.current) {
        audioAnalyzerRef.current = new AudioAnalyzer()
      }

      // Устанавливаем выбранное аудиоустройство
      if (settings.selectedDevice) {
        audioAnalyzerRef.current.setAudioDevice(settings.selectedDevice)
      }

      // Инициализируем анализатор
      const initialized = await audioAnalyzerRef.current.initialize()
      if (initialized) {
        // Устанавливаем настройки
        audioAnalyzerRef.current.setSensitivity(settings.sensitivity)
        audioAnalyzerRef.current.setNoiseThreshold(settings.noiseThreshold)

        // Запускаем анализ звука
        audioAnalyzerRef.current.startListening((note, frequency) => {
          handleNoteDetectedRef.current?.(note, frequency)
        })
      } else {
        console.error('Не удалось инициализировать микрофон')
        alert('Не удалось получить доступ к микрофону. Проверьте разрешения браузера.')
        setState(prev => ({ ...prev, isMicrophoneEnabled: false }))
      }
    } catch (error) {
      console.error('Ошибка при инициализации аудио анализатора:', error)
      alert('Произошла ошибка при инициализации микрофона.')
      setState(prev => ({ ...prev, isMicrophoneEnabled: false }))
    }
  }, [settings.sensitivity, settings.noiseThreshold, settings.selectedDevice])

  /**
   * Останавливает микрофон
   */
  const stopMicrophone = useCallback(() => {
    if (audioAnalyzerRef.current) {
      audioAnalyzerRef.current.stopListening()
      audioAnalyzerRef.current.dispose()
      audioAnalyzerRef.current = null
    }
  }, [])

  // Обработчик клика по ладу
  const handleFretClick = useCallback((stringNumber: number, fretIndex: number) => {
    if (state.gameMode === 'sing' || state.autoPlay || !state.isPlaying || targetSequence.length === 0) return

    // Проверяем, что клик по правильной струне
    if (stringNumber !== selectedString) {
      return
    }

    // Если включен микрофон, не обрабатываем клики
    if (state.isMicrophoneEnabled) {
      return
    }

    if (!state.muteBtnSound) {
      // Вычисляем MIDI ноту для этой позиции на грифе
      const openStringMidi = getOpenStringMidi(stringNumber)
      const clickedMidi = openStringMidi + fretIndex + 12 // +12 полутонов (октава выше)
      audio.playNote(clickedMidi)
    }

    const beatDuration = (60 / state.tempo) * 1000
    const targetNote = targetSequence[currentIndex] as Note & { fret: number }
    if (!targetNote) {
      return
    }

    // Проверяем только лад, так как струна уже зафиксирована
    const isCorrect = fretIndex === targetNote.fret

    // Устанавливаем статус ноты для анимации на ФАКТИЧЕСКИ НАЖАТОЙ ноте
    const clickedNoteKey = `${stringNumber}-${fretIndex}`
    setNoteStatus(prev => ({ ...prev, [clickedNoteKey]: isCorrect }))

    // Сбрасываем статус через время анимации
    setTimeout(() => {
      setNoteStatus(prev => ({ ...prev, [clickedNoteKey]: null }))
    }, 800)

    if (isCorrect) {
      setCurrentIndex(prev => prev + 1)
      setState(prev => ({ ...prev, correct: prev.correct + 1 }))
      if (currentIndex === targetSequence.length - 1) {
        const nextSequenceTimeout = setTimeout(() => {
          const newSequence = generateTargetSequence()
          playSequence(newSequence, true)
        }, beatDuration)
        timeoutRefs.current.push(nextSequenceTimeout)
      }
    } else {
      setState(prev => ({ ...prev, incorrect: prev.incorrect + 1 }))
      if (state.repeatOnError) {
        const repeatTimeout = setTimeout(() => {
          playSequence(targetSequence, true)
        }, beatDuration)
        timeoutRefs.current.push(repeatTimeout)
      }
    }
  }, [state, targetSequence, currentIndex, selectedString, audio, generateTargetSequence, playSequence])

  // Эффект для обновления времени и скорости
  useEffect(() => {
    if (!state.isPlaying || !gameStartTime) return

    const interval = setInterval(() => {
      const now = Date.now()
      const elapsed = Math.floor((now - gameStartTime) / 1000)
      setElapsedTime(elapsed)

      // Обновляем скорость
      if (state.correct > 0 && elapsed > 0) {
        const speed = (state.correct / elapsed) * 60 // ноты в минуту
        setState(prev => ({ ...prev, speed: Math.round(speed) }))
      }
    }, 1000)

    return () => clearInterval(interval)
  }, [state.isPlaying, state.correct, gameStartTime])

  // Оптимизированные localStorage эффекты
  const settingsKey = useMemo(() => {
    const { key, scale, difficultyLevel, gameMode } = state;
    return `pitchTrainerSettings-${gameMode}-${key}-${scale}-${difficultyLevel}`;
  }, [state.key, state.scale, state.difficultyLevel, state.gameMode]);

  // Загружает настройки при изменении ключевой комбинации
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem(settingsKey);
      if (savedSettings) {
        const { tempo, stringSelectionMode } = JSON.parse(savedSettings);
        setState(prev => ({
          ...prev,
          tempo: tempo !== undefined && tempo !== null ? tempo : initialState.tempo,
          stringSelectionMode: stringSelectionMode || initialState.stringSelectionMode
        }));
      } else {
        setState(prev => ({
          ...prev,
          tempo: initialState.tempo,
          stringSelectionMode: initialState.stringSelectionMode
        }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      setState(prev => ({
        ...prev,
        tempo: initialState.tempo,
        stringSelectionMode: initialState.stringSelectionMode
      }));
    }
  }, [settingsKey]);

  // Сохраняет настройки и последние параметры
  useEffect(() => {
    try {
      const { key, scale, difficultyLevel, gameMode, tempo, stringSelectionMode } = state;
      // Сохраняем настройки
      localStorage.setItem(settingsKey, JSON.stringify({ tempo, stringSelectionMode }));
      // Сохраняем последние параметры
      localStorage.setItem('pitchTrainerLastParams', JSON.stringify({ key, scale, difficultyLevel, gameMode, stringSelectionMode }));
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }, [settingsKey, state.tempo, state.stringSelectionMode]);

  // Эффект для управления микрофоном при изменении состояния
  useEffect(() => {
    if (state.isMicrophoneEnabled) {
      startMicrophone()
    } else {
      stopMicrophone()
    }
  }, [state.isMicrophoneEnabled, startMicrophone, stopMicrophone])

  // Очистка таймаутов при размонтировании
  useEffect(() => {
    return () => {
      timeoutRefs.current.forEach(clearTimeout)
      audioRef.current.stopBass()
      // Очищаем микрофон при размонтировании
      stopMicrophone()
    }
  }, [stopMicrophone]) // Убираем зависимость от audio, используем ref

  // Компоненты настроек
  const SettingsSection: React.FC<{ title: string; children: React.ReactNode; disabled?: boolean }> = ({ title, children, disabled }) => (
    <div className={disabled ? 'opacity-50 pointer-events-none' : ''}>
      <Label className="text-lg font-bold mb-2">{title}</Label>
      <div className="space-y-2 ml-4">{children}</div>
    </div>
  )

  const SwitchSetting: React.FC<{ id: string; label: string; checked: boolean; onChange: (checked: boolean) => void; disabled?: boolean }> = ({ id, label, checked, onChange, disabled }) => (
    <div className="flex items-center justify-between cursor-pointer">
      <Label htmlFor={id} className={`text-sm ${disabled ? 'text-gray-400' : 'text-gray-700'}`}>{label}</Label>
      <Switch
        id={id}
        checked={checked}
        onCheckedChange={onChange}
        disabled={disabled}
      />
    </div>
  )

  // Диалог настроек
  const renderSettingsDialog = () => (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogContent className="max-w-md w-full h-[90vh] overflow-y-auto p-4 sm:p-6 sm:max-w-[90vw]">
        <DialogHeader>
          <DialogTitle>Настройки</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-2 overflow-y-auto">
          <SettingsSection title="Режим игры">
            {['standard', 'repeat', 'sing'].map((mode) => (
              <SwitchSetting
                key={mode}
                id={`${mode}Mode`}
                label={mode === 'standard' ? 'Стандартный' : mode === 'repeat' ? 'Повторяем на инструменте' : 'Поем голосом'}
                checked={state.gameMode === mode}
                onChange={(checked: boolean) => {
                  if (checked) {
                    setState(prev => ({
                      ...prev,
                      gameMode: mode as GameMode,
                      autoPlay: mode === 'repeat',
                      showNote: mode === 'sing' ? true : prev.showNote
                    }))
                  }
                }}
              />
            ))}
          </SettingsSection>

                              <SettingsSection title="" disabled={state.gameMode === 'standard'}>
            <div className="flex items-center space-x-2">
              <span className="text-sm whitespace-nowrap">Темп:</span>
              <div className="flex-grow">
                                <Slider
                  id="bpm"
                  min={30}
                  max={240}
                  step={1}
                  value={state.tempo}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    if (!isNaN(value)) {
                      setState(prev => ({ ...prev, tempo: value }))
                    }
                  }}
                />
              </div>
              <span className="text-sm whitespace-nowrap">{state.tempo} BPM</span>
            </div>
          </SettingsSection>

          <SettingsSection title="">
            <div className="flex items-center space-x-2">
              <span className="text-sm whitespace-nowrap">Сложность:</span>
              <div className="flex-grow">
                <Slider
                  id="difficulty"
                  min={1}
                  max={5}
                  step={1}
                  value={DIFFICULTY_MAP[state.difficultyLevel]}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    if (!isNaN(value) && value in DIFFICULTY_REVERSE_MAP) {
                      setState(prev => ({
                        ...prev,
                        difficultyLevel: DIFFICULTY_REVERSE_MAP[value as keyof typeof DIFFICULTY_REVERSE_MAP]
                      }))
                    }
                  }}
                />
              </div>
              <span className="text-sm whitespace-nowrap">{state.difficultyLevel}</span>
            </div>
          </SettingsSection>

          <SettingsSection title="Тональность">
            <div className="grid grid-cols-12 gap-2">
              <div className="col-span-3">
                <Select
                  options={Object.keys(noteMidiNumbers).map((key) => ({ label: key, value: key }))}
                  value={state.key}
                  onValueChange={(value) => setState(prev => ({ ...prev, key: value }))}
                  disabled={state.isRandomKey}
                  placeholder="Тон."
                />
              </div>
              <div className="col-span-2 flex items-center justify-center">
                <button
                  type="button"
                  onClick={() => setState(prev => ({ ...prev, isRandomKey: !prev.isRandomKey }))}
                  className={`w-10 h-10 rounded flex items-center justify-center ${state.isRandomKey ? "bg-blue-600 text-white" : "border border-gray-300"}`}
                >
                  <Shuffle size={20} />
                </button>
              </div>
              <div className="col-span-5">
                <Select
                  options={[
                    { label: 'Chromatic', value: 'Chromatic' },
                    ...Object.keys(scalePatterns)
                      .filter(scale => scale !== 'Chromatic')
                      .map(scale => ({ label: scale, value: scale }))
                  ]}
                  value={state.scale}
                  onValueChange={(value) => setState(prev => ({ ...prev, scale: value as Scale }))}
                  disabled={state.isRandomScale}
                  placeholder="Выберите звукоряд"
                />
              </div>
            </div>
          </SettingsSection>

          <SettingsSection title="Звук">
            <div className="flex flex-col space-y-2">
              <SwitchSetting
                id="enableBass"
                label="Включить бас"
                checked={state.isBassEnabled}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, isBassEnabled: checked }))}
              />
              <SwitchSetting
                id="enableButtonSound"
                label="Включить звук кнопок"
                checked={!state.muteBtnSound}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, muteBtnSound: !checked }))}
              />
              <SwitchSetting
                id="enableMicrophone"
                label="Распознавание нот с микрофона"
                checked={state.isMicrophoneEnabled}
                onChange={handleMicrophoneToggle}
                disabled={state.gameMode === 'sing' || state.autoPlay}
              />
            </div>
          </SettingsSection>

          <SettingsSection title="Выбор струны">
            <div className="flex flex-col space-y-2">
              <SwitchSetting
                id="randomString"
                label="Случайно"
                checked={state.stringSelectionMode === 'random'}
                onChange={(checked: boolean) => {
                  if (checked) setState(prev => ({ ...prev, stringSelectionMode: 'random' }))
                }}
              />
              <SwitchSetting
                id="ascendingString"
                label="Последовательно 6→1"
                checked={state.stringSelectionMode === 'ascending'}
                onChange={(checked: boolean) => {
                  if (checked) setState(prev => ({ ...prev, stringSelectionMode: 'ascending' }))
                }}
              />
              <SwitchSetting
                id="descendingString"
                label="Последовательно 1→6"
                checked={state.stringSelectionMode === 'descending'}
                onChange={(checked: boolean) => {
                  if (checked) setState(prev => ({ ...prev, stringSelectionMode: 'descending' }))
                }}
              />
            </div>
          </SettingsSection>

          <SettingsSection title="Подсказка">
            <div className="flex flex-col space-y-2">
              <SwitchSetting
                id="showNotesAsSteps"
                label="Показывать ступени вместо нот"
                checked={state.showNotesAsSteps}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, showNotesAsSteps: checked }))}
              />
              <SwitchSetting
                id="showTargetNote"
                label="Показывать текущую ноту"
                checked={state.showNote}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, showNote: checked }))}
                disabled={state.gameMode === 'sing'}
              />
              <SwitchSetting
                id="repeatTargetOnError"
                label="Повторять ноты при ошибке"
                checked={state.repeatOnError}
                onChange={(checked: boolean) => setState(prev => ({ ...prev, repeatOnError: checked }))}
                disabled={state.gameMode === 'repeat' || state.gameMode === 'sing'}
              />
            </div>
          </SettingsSection>
        </div>
        <Button onClick={() => setIsDialogOpen(false)} className="w-full mt-4 py-2 text-sm">
          Применить
        </Button>
      </DialogContent>
    </Dialog>
  )



  // Создаем ноты для отображения на грифе
  const fretboardNotes: NoteDisplay[] = useMemo(() => {
    const notes: NoteDisplay[] = []

    // Показываем все ноты гаммы на выбранной струне
    if (state.isPlaying && selectedString > 0) {
      const scaleNotesOnString = getScaleNotesOnString(selectedString, state.key, state.scale)

      scaleNotesOnString.forEach((noteInfo, index) => {
        const matchingNote = allNotes.find(n => n.note === noteInfo.note)
        const isCurrentTarget = state.showNote && currentFretboardPosition &&
          currentFretboardPosition.string === selectedString &&
          currentFretboardPosition.fret === noteInfo.fret

        // Выбираем что показывать: ноты или ступени
        const displayLabel = state.showNotesAsSteps ? noteInfo.note : noteInfo.actualNote

        // Проверяем статус ноты для анимации
        const noteKey = `${selectedString}-${noteInfo.fret}`
        const currentNoteStatus = noteStatus[noteKey]

        // Определяем цвет и статус правильности
        const noteColor = isCurrentTarget ? 'bg-yellow-400' : (matchingNote?.color || 'bg-gray-400')

        notes.push({
          id: `scale-note-${index}`,
          string: selectedString,
          fret: noteInfo.fret,
          label: displayLabel,
          color: noteColor,
          isHighlighted: isCurrentTarget === null ? undefined : isCurrentTarget,
          isCorrect: currentNoteStatus === null ? undefined : currentNoteStatus // Используем встроенное свойство для анимации
        })
      })

      // Добавляем ноты, которые были сыграны, но не входят в гамму (для анимации ошибок)
      Object.keys(noteStatus).forEach(noteKey => {
        const [stringStr, fretStr] = noteKey.split('-')
        const string = parseInt(stringStr)
        const fret = parseInt(fretStr)

        // Проверяем, что это нота на текущей выбранной струне
        if (string === selectedString && noteStatus[noteKey] !== null) {
          // Проверяем, есть ли уже эта нота в списке (из гаммы)
          const existingNote = notes.find(n => n.string === string && n.fret === fret)

          if (!existingNote) {
            // Добавляем ноту, которая не входит в гамму, но была сыграна
            // Вычисляем название ноты для этой позиции
            const openStringMidi = getOpenStringMidi(string)
            const noteMidi = openStringMidi + fret
            const noteName = NOTE_NAMES[noteMidi % 12]
            const octave = Math.floor(noteMidi / 12) - 1
            const noteWithOctave = noteName + octave

            notes.push({
              id: `played-note-${noteKey}`,
              string: string,
              fret: fret,
              label: noteWithOctave,
              color: 'bg-gray-500', // Серый цвет для нот вне гаммы
              isCorrect: noteStatus[noteKey] === null ? undefined : noteStatus[noteKey]
            })
          }
        }
      })
    }

    return notes
  }, [state.isPlaying, state.showNote, state.showNotesAsSteps, selectedString, state.key, state.scale, currentFretboardPosition, getScaleNotesOnString, noteStatus])

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-2 relative">
      {/* CSS анимации для визуальной обратной связи */}
      <style>{`
        @keyframes flash-red { 0%, 100% { background-color: inherit; } 50% { background-color: #F44336; } }
        .animate-flash-red { animation: flash-red 0.5s; }
        @keyframes flash-green { 0%, 100% { background-color: inherit; } 50% { background-color: #4CAF50; } }
        .animate-flash-green { animation: flash-green 0.5s; }
        @keyframes pulse-green {
          0% { background-color: #16a34a; transform: scale(1); box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
          25% { background-color: #22c55e; transform: scale(1.1); box-shadow: 0 0 0 8px rgba(34, 197, 94, 0.4); }
          50% { background-color: #4ade80; transform: scale(1.15); box-shadow: 0 0 0 12px rgba(34, 197, 94, 0.2); }
          75% { background-color: #22c55e; transform: scale(1.1); box-shadow: 0 0 0 8px rgba(34, 197, 94, 0.4); }
          100% { background-color: #16a34a; transform: scale(1); box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
        }
        .animate-pulse-green { animation: pulse-green 0.8s ease-in-out; }
        @keyframes pulse-red {
          0% { background-color: #dc2626; transform: scale(1); box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
          25% { background-color: #ef4444; transform: scale(1.1); box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.4); }
          50% { background-color: #f87171; transform: scale(1.15); box-shadow: 0 0 0 12px rgba(239, 68, 68, 0.2); }
          75% { background-color: #ef4444; transform: scale(1.1); box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.4); }
          100% { background-color: #dc2626; transform: scale(1); box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
        }
        .animate-pulse-red { animation: pulse-red 0.8s ease-in-out; }
      `}</style>

      {/* Кнопка "Назад" - перемещена правее, чтобы не перекрывать гамбургер-меню */}
      <BackButton className="absolute top-4 left-16" />

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md w-full max-w-md aspect-[9/16] flex flex-col items-center justify-between relative p-4 text-gray-900 dark:text-gray-100">
        <div className="w-full flex justify-between items-center mb-4">
          <Button
            className="icon-button active:scale-90 hover:brightness-90 transition-transform duration-100 w-12 h-12"
            onClick={replaySequence}
            disabled={!state.isPlaying || state.gameMode === 'repeat'}
          >
            <RefreshCw size={20} />
          </Button>
          <Button
            onClick={handleStart}
            className={`flex-grow mx-2 font-bold py-1 px-4 rounded-md text-sm shadow-lg transition-all duration-300 active:scale-90 hover:brightness-90 h-12 ${
              state.isPlaying
                ? 'bg-red-500 hover:bg-red-600 text-white active:bg-red-700 active:shadow-inner'
                : 'bg-green-500 hover:bg-green-600 text-white active:bg-green-700 active:shadow-inner'
            }`}
          >
            {state.isPlaying ? 'Стоп' : 'Старт'}
          </Button>
          <Button
              className="icon-button active:scale-90 hover:brightness-90 transition-transform duration-100 w-12 h-12"
              onClick={() => setIsDialogOpen(true)}
            >
              <Settings size={20} />
            </Button>
        </div>
        <div className="mb-4 text-center w-full">
          <p className="text-sm sm:text-base font-bold">Время: {formatTime(elapsedTime)}</p>
          <p className="text-sm sm:text-base font-bold">
            Счет: <span className="text-green-500">{state.correct}</span>:<span className="text-red-500">{state.incorrect}</span>
            <span className="ml-1">
              <span className="text-green-500">{calculatePercentage(state.correct, state.correct + state.incorrect)}%</span>:
              <span className="text-red-500">{calculatePercentage(state.incorrect, state.correct + state.incorrect)}%</span>
            </span>
          </p>
          <p className="text-sm sm:text-base font-bold">
            Скорость: {state.speed} нот/мин : {state.speed * 4} BPM
          </p>
        </div>

        {/* Гитарный гриф */}
        <div className="flex-grow w-full">
          <GuitarFretboard
            highlightedString={selectedString > 0 ? selectedString : undefined}
            animate={isPlayback}
            notes={fretboardNotes}
            onFretClick={state.isPlaying && !state.isMicrophoneEnabled ? handleFretClick : undefined}
          />
        </div>

        {renderSettingsDialog()}
      </div>
    </div>
  )
}

export default GuitarPitchTrainer
