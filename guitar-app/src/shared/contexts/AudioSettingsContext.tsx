import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { AudioInputDevice, getBaseAudioDevices, getChannelsForDevice, onDeviceChange } from '../utils/audioDevices';

// Интерфейс для настроек аудио
export interface AudioSettings {
  selectedDevice: string | null;
  selectedChannel: number;
  sensitivity: number;
  noiseThreshold: number;
}

// Интерфейс для контекста аудио настроек
interface AudioSettingsContextType {
  settings: AudioSettings;
  updateSettings: (newSettings: Partial<AudioSettings>) => void;
  audioDevices: AudioInputDevice[];
  availableChannels: Array<{ index: number; label: string; value: number }>;
  isLoadingDevices: boolean;
  isLoadingChannels: boolean;
  loadAudioDevices: () => Promise<void>;
  handleDeviceChange: (deviceId: string | null) => Promise<void>;
  handleChannelChange: (channel: number) => Promise<void>;
}

// Создаем контекст для аудио настроек
const AudioSettingsContext = createContext<AudioSettingsContextType | null>(null);

// Хук для использования контекста аудио настроек
export const useAudioSettings = () => {
  const context = useContext(AudioSettingsContext);
  if (!context) {
    throw new Error('useAudioSettings must be used within AudioSettingsProvider');
  }
  return context;
};

// Функция для загрузки начальных настроек из localStorage
const getInitialSettings = (): AudioSettings => {
  const defaultSettings: AudioSettings = {
    selectedDevice: null,
    selectedChannel: 1,
    sensitivity: 0.05,
    noiseThreshold: 35,
  };

  try {
    const savedSettings = localStorage.getItem('audioSettings');
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings);
      return { ...defaultSettings, ...parsed };
    }
  } catch (error) {
    console.error('Ошибка при загрузке настроек из localStorage:', error);
  }

  return defaultSettings;
};

// Провайдер для аудио настроек
export const AudioSettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<AudioSettings>(getInitialSettings);

  const [audioDevices, setAudioDevices] = useState<AudioInputDevice[]>([]);
  const [availableChannels, setAvailableChannels] = useState<Array<{ index: number; label: string; value: number }>>([]);
  const [isLoadingDevices, setIsLoadingDevices] = useState(false);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Функция для обновления настроек
  const updateSettings = useCallback((newSettings: Partial<AudioSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  // Загрузка аудиоустройств
  const loadAudioDevices = useCallback(async () => {
    try {
      setIsLoadingDevices(true);
      const devices = await getBaseAudioDevices();
      setAudioDevices(devices);

      // Проверяем, доступно ли выбранное устройство
      if (settings.selectedDevice && !devices.some((d: AudioInputDevice) => d.deviceId === settings.selectedDevice)) {
        console.warn('Выбранное аудиоустройство больше не доступно, сбрасываем на устройство по умолчанию');
        updateSettings({ selectedDevice: null });
      }
    } catch (error) {
      console.error('Ошибка при загрузке аудиоустройств:', error);
    } finally {
      setIsLoadingDevices(false);
    }
  }, [settings.selectedDevice, updateSettings]);

  // Обработчик изменения устройства
  const handleDeviceChange = useCallback(async (deviceId: string | null) => {
    updateSettings({ selectedDevice: deviceId });

    if (deviceId) {
      try {
        setIsLoadingChannels(true);
        const channels = await getChannelsForDevice(deviceId);
        const channelOptions = channels.map((channel, index) => ({
          index,
          label: `Вход ${index + 1}${channel.label ? ` (${channel.label})` : ''}`,
          value: index + 1
        }));
        setAvailableChannels(channelOptions);
        
        // Если текущий канал больше доступного количества, сбрасываем на первый
        if (settings.selectedChannel > channelOptions.length) {
          updateSettings({ selectedChannel: 1 });
        }
      } catch (error) {
        console.error('Ошибка при загрузке каналов:', error);
        setAvailableChannels([]);
      } finally {
        setIsLoadingChannels(false);
      }
    } else {
      setAvailableChannels([]);
    }
  }, [settings.selectedChannel, updateSettings]);

  // Обработчик изменения канала
  const handleChannelChange = useCallback(async (channel: number) => {
    updateSettings({ selectedChannel: channel });
  }, [updateSettings]);

  // Загружаем устройства при монтировании
  useEffect(() => {
    loadAudioDevices();
  }, [loadAudioDevices]);

  // Подписываемся на изменения устройств
  useEffect(() => {
    const unsubscribe = onDeviceChange(() => {
      loadAudioDevices();
    });

    return unsubscribe;
  }, [loadAudioDevices]);



  // Отмечаем, что компонент инициализирован
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // Загружаем каналы для выбранного устройства при инициализации
  useEffect(() => {
    if (settings.selectedDevice && isInitialized) {
      const loadChannelsForSelectedDevice = async () => {
        try {
          setIsLoadingChannels(true);
          const channels = await getChannelsForDevice(settings.selectedDevice!);
          const channelOptions = channels.map((channel, index) => ({
            index,
            label: `Вход ${index + 1}${channel.label ? ` (${channel.label})` : ''}`,
            value: index + 1
          }));
          setAvailableChannels(channelOptions);

          // Если текущий канал больше доступного количества, сбрасываем на первый
          if (settings.selectedChannel > channelOptions.length) {
            updateSettings({ selectedChannel: 1 });
          }
        } catch (error) {
          console.error('Ошибка при загрузке каналов:', error);
          setAvailableChannels([]);
        } finally {
          setIsLoadingChannels(false);
        }
      };

      loadChannelsForSelectedDevice();
    } else if (!settings.selectedDevice) {
      // Если устройство не выбрано, очищаем каналы
      setAvailableChannels([]);
    }
  }, [settings.selectedDevice, isInitialized, settings.selectedChannel, updateSettings]);

  // Сохраняем настройки в localStorage (только после инициализации)
  useEffect(() => {
    if (!isInitialized) return;

    try {
      localStorage.setItem('audioSettings', JSON.stringify(settings));
    } catch (error) {
      console.error('Ошибка при сохранении настроек аудио:', error);
    }
  }, [settings, isInitialized]);

  const contextValue: AudioSettingsContextType = {
    settings,
    updateSettings,
    audioDevices,
    availableChannels,
    isLoadingDevices,
    isLoadingChannels,
    loadAudioDevices,
    handleDeviceChange,
    handleChannelChange,
  };

  return (
    <AudioSettingsContext.Provider value={contextValue}>
      {children}
    </AudioSettingsContext.Provider>
  );
};
