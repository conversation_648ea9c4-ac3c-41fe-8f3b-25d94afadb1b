import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, Home, Guitar, Music, Headphones, BookOpen, Settings } from 'lucide-react';

interface HamburgerMenuProps {
  className?: string;
}

// Определяем типы для элементов меню
type MenuItemLink = {
  to: string;
  label: string;
  icon: React.ReactNode;
  divider?: never; // Убедимся, что у ссылки нет свойства divider
};

type MenuItemDivider = {
  divider: true;
  to?: never; // Убедимся, что у разделителя нет свойства to
  label?: never;
  icon?: never;
};

type MenuItemType = MenuItemLink | MenuItemDivider;

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  // Применяем определенный тип к массиву
  const menuItems: MenuItemType[] = [
    { to: '/', label: 'Главная', icon: <Home className="h-4 w-4 mr-2" /> },
    { to: '/guitar-fretboard', label: 'Ноты и интервалы', icon: <Guitar className="h-4 w-4 mr-2" /> },
    { to: '/pitch-trainer', label: 'Tonal Pitch Trainer', icon: <Music className="h-4 w-4 mr-2" /> },
    { to: '/note-recognition', label: 'Настройка распознавания нот', icon: <Headphones className="h-4 w-4 mr-2" /> },
    { to: '/chord-trainer', label: 'Тренажер аккордов', icon: <Guitar className="h-4 w-4 mr-2" /> },
    { to: '/name-the-chord', label: 'Назови аккорд', icon: <Guitar className="h-4 w-4 mr-2" /> },
    { to: '/score-counter', label: 'Счетчик очков', icon: <Music className="h-4 w-4 mr-2" /> },
    { to: '/guitar-neck-editor', label: 'Редактор грифа', icon: <Guitar className="h-4 w-4 mr-2" /> },
    { to: '/midi-chord-generator', label: 'Генератор аккордов', icon: <Music className="h-4 w-4 mr-2" /> },
    { divider: true },
    { to: '/typography', label: 'Типографика', icon: <BookOpen className="h-4 w-4 mr-2" /> },
    { to: '/ui-components', label: 'UI Компоненты', icon: <Settings className="h-4 w-4 mr-2" /> },
  ];

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={toggleMenu}
        className="flex items-center justify-center w-10 h-10 p-0 rounded-full bg-blue-500 dark:bg-blue-600 text-white hover:bg-blue-600 dark:hover:bg-blue-700 transition-colors duration-300"
        aria-label="Открыть меню"
      >
        {isOpen ? (
          <X className="h-5 w-5" />
        ) : (
          <Menu className="h-5 w-5" />
        )}
      </button>

      {isOpen && (
        <div className="absolute left-0 top-full mt-2 w-64 rounded-md shadow-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 z-50 max-h-[80vh] overflow-y-auto">
          <div className="py-1">
            {menuItems.map((item, index) => (
              item.divider ? (
                <div key={`divider-${index}`} className="border-t border-gray-200 dark:border-gray-700 my-2"></div>
              ) : (
                // Теперь TypeScript уверен, что item.to это строка
                <Link
                  key={item.to}
                  to={item.to}
                  className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-blue-500 hover:text-white dark:hover:bg-blue-600"
                  onClick={() => setIsOpen(false)}
                >
                  {item.icon}
                  {item.label}
                </Link>
              )
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default HamburgerMenu;
