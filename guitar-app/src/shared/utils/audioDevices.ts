/**
 * audioDevices.ts - Утилиты для работы с аудиоустройствами
 * 
 * Предоставляет функции для получения списка доступных аудиоустройств ввода,
 * их фильтрации и управления выбором устройства для распознавания нот.
 */

// Интерфейс для описания аудиоустройства
export interface AudioInputDevice {
  deviceId: string;
  label: string;
  groupId: string;
  kind: 'audioinput';
  isDefault: boolean;
  channelCount?: number;
  sampleRate?: number;
  displayName?: string; // Отформатированное название для UI
}

/**
 * Получает список всех доступных аудиоустройств ввода
 * @returns Promise с массивом аудиоустройств ввода
 */
export async function getAudioInputDevices(): Promise<AudioInputDevice[]> {
  try {
    // Проверяем поддержку API
    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
      console.warn('MediaDevices API не поддерживается');
      return [];
    }

    // Получаем все устройства
    const devices = await navigator.mediaDevices.enumerateDevices();
    
    // Фильтруем только аудиоустройства ввода
    const audioInputs = devices
      .filter(device => device.kind === 'audioinput')
      .map((device, index) => ({
        deviceId: device.deviceId,
        label: device.label || `Микрофон ${index + 1}`,
        groupId: device.groupId,
        kind: device.kind as 'audioinput',
        isDefault: device.deviceId === 'default'
      }));

    return audioInputs;
  } catch (error) {
    return [];
  }
}

/**
 * Получает список аудиоустройств ввода с запросом разрешений
 * Эта функция запрашивает разрешение на доступ к микрофону,
 * что позволяет получить реальные названия устройств
 * @returns Promise с массивом аудиоустройств ввода
 */
export async function getAudioInputDevicesWithPermission(): Promise<AudioInputDevice[]> {
  try {
    // Сначала запрашиваем разрешение на доступ к микрофону
    // Это необходимо для получения реальных названий устройств
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    
    // Останавливаем поток, так как он нам нужен только для получения разрешения
    stream.getTracks().forEach(track => track.stop());
    
    // Теперь получаем список устройств с реальными названиями
    return await getAudioInputDevices();
  } catch (error) {
    console.warn('Не удалось получить разрешение на доступ к микрофону:', error);
    // Возвращаем список без разрешения (названия могут быть скрыты)
    return await getAudioInputDevices();
  }
}

/**
 * Проверяет доступность конкретного аудиоустройства
 * @param deviceId ID устройства для проверки
 * @returns Promise<boolean> - доступно ли устройство
 */
export async function isAudioDeviceAvailable(deviceId: string): Promise<boolean> {
  try {
    const devices = await getAudioInputDevices();
    return devices.some(device => device.deviceId === deviceId);
  } catch (error) {
    console.error('Ошибка при проверке доступности устройства:', error);
    return false;
  }
}

/**
 * Получает устройство по умолчанию
 * @returns Promise с устройством по умолчанию или null
 */
export async function getDefaultAudioInputDevice(): Promise<AudioInputDevice | null> {
  try {
    const devices = await getAudioInputDevices();
    
    // Ищем устройство с deviceId 'default'
    const defaultDevice = devices.find(device => device.deviceId === 'default');
    if (defaultDevice) {
      return defaultDevice;
    }
    
    // Если нет устройства 'default', возвращаем первое доступное
    return devices.length > 0 ? devices[0] : null;
  } catch (error) {
    console.error('Ошибка при получении устройства по умолчанию:', error);
    return null;
  }
}

/**
 * Тестирует аудиоустройство, пытаясь получить поток с него
 * @param deviceId ID устройства для тестирования
 * @returns Promise<boolean> - работает ли устройство
 */
export async function testAudioDevice(deviceId: string): Promise<boolean> {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        deviceId: deviceId ? { exact: deviceId } : undefined
      }
    });
    
    // Проверяем, что поток активен
    const isActive = stream.active && stream.getAudioTracks().length > 0;
    
    // Останавливаем поток
    stream.getTracks().forEach(track => track.stop());
    
    return isActive;
  } catch (error) {
    console.error(`Ошибка при тестировании устройства ${deviceId}:`, error);
    return false;
  }
}

/**
 * Форматирует название устройства для отображения в UI
 * @param device Устройство для форматирования
 * @returns Отформатированное название
 */
export function formatDeviceLabel(device: AudioInputDevice): string {
  if (device.isDefault) {
    return `${device.label} (по умолчанию)`;
  }
  return device.label;
}

/**
 * Проверяет, поддерживается ли выбор аудиоустройств в браузере
 * @returns boolean - поддерживается ли функциональность
 */
export function isDeviceSelectionSupported(): boolean {
  return !!(
    navigator.mediaDevices &&
    typeof navigator.mediaDevices.enumerateDevices === 'function' &&
    typeof navigator.mediaDevices.getUserMedia === 'function'
  );
}

/**
 * Создает constraints для getUserMedia с указанным устройством
 * @param deviceId ID устройства (может быть null для устройства по умолчанию)
 * @returns MediaStreamConstraints для getUserMedia
 */
export function createAudioConstraints(deviceId: string | null): MediaStreamConstraints {
  const audioConstraints: MediaTrackConstraints = {
    echoCancellation: false,
    noiseSuppression: false,
    autoGainControl: false,
    sampleRate: 44100,
    channelCount: 1
  };

  if (deviceId && deviceId !== 'default') {
    // Проверяем, указан ли конкретный канал
    if (deviceId.includes(':channel:')) {
      const [baseDeviceId, , channelStr] = deviceId.split(':');
      const channelIndex = parseInt(channelStr, 10);

      audioConstraints.deviceId = { exact: baseDeviceId };
      // Для конкретного канала можем добавить дополнительные constraints
      if (channelIndex > 1) {
        // Некоторые браузеры поддерживают выбор канала через channelCount
        audioConstraints.channelCount = { exact: 1 };
      }
    } else {
      audioConstraints.deviceId = { exact: deviceId };
    }
  }

  return {
    audio: audioConstraints,
    video: false
  };
}

/**
 * Извлекает базовый deviceId из расширенного deviceId с каналом
 * @param deviceId Расширенный deviceId (может содержать :channel:N)
 * @returns Базовый deviceId
 */
export function getBaseDeviceId(deviceId: string): string {
  if (deviceId.includes(':channel:')) {
    return deviceId.split(':')[0];
  }
  return deviceId;
}

/**
 * Извлекает номер канала из расширенного deviceId
 * @param deviceId Расширенный deviceId
 * @returns Номер канала или null если не указан
 */
export function getChannelNumber(deviceId: string): number | null {
  if (deviceId.includes(':channel:')) {
    const channelStr = deviceId.split(':')[2];
    const channel = parseInt(channelStr, 10);
    return isNaN(channel) ? null : channel;
  }
  return null;
}

/**
 * Слушатель изменений в списке устройств
 * @param callback Функция, вызываемая при изменении списка устройств
 * @returns Функция для отписки от событий
 */
export function onDeviceChange(callback: () => void): () => void {
  if (!navigator.mediaDevices || !navigator.mediaDevices.addEventListener) {
    console.warn('События изменения устройств не поддерживаются');
    return () => {};
  }

  navigator.mediaDevices.addEventListener('devicechange', callback);
  
  return () => {
    navigator.mediaDevices.removeEventListener('devicechange', callback);
  };
}

/**
 * Получает информацию о возможностях аудиоустройства
 * @param deviceId ID устройства
 * @returns Promise с информацией о возможностях устройства
 */
export async function getDeviceCapabilities(deviceId: string): Promise<MediaTrackCapabilities | null> {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: { deviceId: deviceId ? { exact: deviceId } : undefined }
    });

    const audioTrack = stream.getAudioTracks()[0];
    const capabilities = audioTrack.getCapabilities ? audioTrack.getCapabilities() : null;

    // Останавливаем поток
    stream.getTracks().forEach(track => track.stop());

    return capabilities;
  } catch (error) {
    console.error(`Ошибка при получении возможностей устройства ${deviceId}:`, error);
    return null;
  }
}

/**
 * Получает расширенную информацию об аудиоустройстве
 * @param device Базовая информация об устройстве
 * @returns Promise с расширенной информацией
 */
export async function getDeviceInfo(device: AudioInputDevice): Promise<AudioInputDevice & {
  capabilities?: MediaTrackCapabilities;
  isWorking?: boolean;
}> {
  try {
    const [capabilities, isWorking] = await Promise.all([
      getDeviceCapabilities(device.deviceId),
      testAudioDevice(device.deviceId)
    ]);

    return {
      ...device,
      capabilities: capabilities || undefined,
      isWorking
    };
  } catch (error) {
    console.error(`Ошибка при получении информации об устройстве ${device.deviceId}:`, error);
    return {
      ...device,
      isWorking: false
    };
  }
}

/**
 * Получает список доступных каналов для устройства
 * @param deviceId ID устройства
 * @returns Promise с информацией о каналах
 */
export async function getDeviceChannels(deviceId: string): Promise<{
  maxChannels: number;
  channels: Array<{ index: number; label: string; deviceId: string }>;
}> {
  try {
    const capabilities = await getDeviceCapabilities(deviceId);

    // Определяем максимальное количество каналов
    // Для профессиональных аудиоинтерфейсов обычно 2-16 каналов
    let maxChannels = capabilities?.channelCount?.max || 2;

    // Если браузер не может определить количество каналов или возвращает 1,
    // предполагаем стандартные значения для разных типов устройств
    if (maxChannels <= 1) {
      // Проверяем название устройства для определения типа
      const devices = await getAudioInputDevices();
      const device = devices.find(d => d.deviceId === deviceId);
      const deviceName = device?.label.toLowerCase() || '';

      // Эвристика для определения типа устройства
      if (deviceName.includes('focusrite') || deviceName.includes('scarlett')) {
        maxChannels = deviceName.includes('solo') ? 2 :
                     deviceName.includes('2i2') ? 2 :
                     deviceName.includes('4i4') ? 4 :
                     deviceName.includes('8i6') ? 8 :
                     deviceName.includes('18i8') ? 8 : 4;
      } else if (deviceName.includes('presonus') || deviceName.includes('audiobox')) {
        maxChannels = deviceName.includes('22vsl') ? 2 :
                     deviceName.includes('44vsl') ? 4 :
                     deviceName.includes('1818vsl') ? 8 : 2;
      } else if (deviceName.includes('behringer') || deviceName.includes('u-phoria')) {
        maxChannels = deviceName.includes('um2') ? 2 :
                     deviceName.includes('umc22') ? 2 :
                     deviceName.includes('umc202') ? 2 :
                     deviceName.includes('umc404') ? 4 :
                     deviceName.includes('umc1820') ? 8 : 2;
      } else if (deviceName.includes('zoom') && deviceName.includes('podtrak')) {
        maxChannels = 4;
      } else if (deviceName.includes('interface') || deviceName.includes('mixer')) {
        // Общий случай для аудиоинтерфейсов
        maxChannels = 4;
      } else {
        // Для обычных микрофонов и веб-камер
        maxChannels = 2;
      }
    }

    // Ограничиваем максимум 16 каналами для производительности
    maxChannels = Math.min(maxChannels, 16);

    // Создаем список каналов
    const channels = [];
    for (let i = 1; i <= maxChannels; i++) {
      channels.push({
        index: i,
        label: `Вход ${i}`,
        deviceId: `${deviceId}:channel:${i}`
      });
    }

    return {
      maxChannels,
      channels
    };
  } catch (error) {
    return {
      maxChannels: 2,
      channels: [
        {
          index: 1,
          label: 'Вход 1',
          deviceId: `${deviceId}:channel:1`
        },
        {
          index: 2,
          label: 'Вход 2',
          deviceId: `${deviceId}:channel:2`
        }
      ]
    };
  }
}

/**
 * Получает базовый список аудиоустройств без каналов
 * @returns Promise с массивом базовых аудиоустройств
 */
export async function getBaseAudioDevices(): Promise<AudioInputDevice[]> {
  try {
    const devices = await getAudioInputDevicesWithPermission();
    return devices.map(device => ({
      ...device,
      displayName: device.label
    }));
  } catch (error) {
    console.error('Ошибка при получении базовых аудиоустройств:', error);
    return [];
  }
}

/**
 * Получает список каналов для конкретного устройства
 * @param deviceId ID устройства
 * @returns Promise с массивом каналов устройства
 */
export async function getChannelsForDevice(deviceId: string): Promise<Array<{ index: number; label: string; value: number }>> {
  try {
    const channelInfo = await getDeviceChannels(deviceId);
    return channelInfo.channels.map(channel => ({
      index: channel.index,
      label: channel.label,
      value: channel.index
    }));
  } catch (error) {
    console.error(`Ошибка при получении каналов для устройства ${deviceId}:`, error);
    return [
      { index: 1, label: 'Вход 1', value: 1 },
      { index: 2, label: 'Вход 2', value: 2 }
    ];
  }
}

/**
 * Создает полный deviceId из базового deviceId и номера канала
 * @param baseDeviceId Базовый ID устройства
 * @param channelNumber Номер канала
 * @returns Полный deviceId с каналом
 */
export function createChannelDeviceId(baseDeviceId: string, channelNumber: number): string {
  return `${baseDeviceId}:channel:${channelNumber}`;
}

/**
 * Получает расширенный список устройств с каналами (для обратной совместимости)
 * @returns Promise с расширенным списком устройств и каналов
 */
export async function getAudioInputDevicesWithChannels(): Promise<AudioInputDevice[]> {
  try {
    const baseDevices = await getAudioInputDevicesWithPermission();
    const expandedDevices: AudioInputDevice[] = [];

    for (const device of baseDevices) {
      // Добавляем основное устройство (все каналы)
      expandedDevices.push({
        ...device,
        displayName: device.label
      });

      // Получаем информацию о каналах
      const channelInfo = await getDeviceChannels(device.deviceId);

      // Если у устройства больше одного канала, добавляем отдельные входы
      if (channelInfo.maxChannels > 1) {
        for (const channel of channelInfo.channels) {
          expandedDevices.push({
            ...device,
            deviceId: channel.deviceId,
            label: `${device.label} - ${channel.label}`,
            displayName: `${device.label} - ${channel.label}`,
            isDefault: false
          });
        }
      } else {
      }
    }

    return expandedDevices;
  } catch (error) {
    console.error('Ошибка при получении устройств с каналами:', error);
    return await getAudioInputDevicesWithPermission();
  }
}
